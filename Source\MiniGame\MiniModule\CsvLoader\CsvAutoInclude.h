#ifndef __CsvAutoInclude_h__
#define __CsvAutoInclude_h__ 
#include <vector> 
#include "AbsCsv.h" 
#include "AiNpcPlayerCsv.h"
#include "AntiFraudCsv.h"
#include "ArchitecturalBlueprintCsv.h"
#include "AvatarDefCsv.h"
#include "BiomeGroupDefCsv.h"
#include "BlockDefCsv.h"
#include "BlockEffectDefCsv.h"
#include "BotConversationsDefCsv.h"
#include "BuffDefCsv.h"
#include "BuffEffectBankCsv.h"
#include "BuffEffectEnumCsv.h"
#include "BuffEffectSlidingCsv.h"
#include "BuildingMaintenanceCsv.h"
#include "ChestSpawnCsv.h"
#include "ColorMixDefCsv.h"
#include "CreateRoleAvatarCsv.h"
#include "CustomGunDefCsv.h"
#include "CustomPrefabInfoCsv.h"
#include "DieInfoCsv.h"
#include "DisassembleCsv.h"
#include "EquipDefCsv.h"
#include "EquipGroupDefCsv.h"
#include "FishingDefCsv.h"
#include "FoodDefCsv.h"
#include "GameLanguageCsv.h"
#include "GameZoneCsv.h"
#include "HorseEggCsv.h"
#include "ItemDefCsv.h"
#include "ItemInHandDefCsv.h"
#include "ItemSkillDefCsv.h"
#include "ItemStatusDefCsv.h"
#include "LuaDefCsv.h"
#include "MergeMobileSupportCsv.h"
#include "MonsterCsv.h"
#include "OperateUIDataCsv.h"
#include "ParticlesCsv.h"
#include "ParticlesStrDefCsv.h"
#include "PhysicsActorCsv.h"
#include "PlayerAttribCsv.h"
#include "ProjectileDefCsv.h"
#include "ResourcePackDefCsv.h"
#include "RoleSkinCsv.h"
#include "RuleOptionCsv.h"
#include "ScoreCsv.h"
#include "SkinActCsv.h"
#include "SkinningToolCsv.h"
#include "SoundStrDefCsv.h"
#include "SprayPaintDefCsv.h"
#include "StringDefCsv.h"
#include "SummonDefCsv.h"
#include "SurviveObjectiveDefCsv.h"
#include "SurviveTaskDefCsv.h"
#include "ToolDefCsv.h"
#include "UgcMaterialCsv.h"
#include "UgcModelCsv.h"
#include "WorkbenchTechCsv.h"
void includeCsvAuto(std::vector<AbsCsv*>& vAbsCsv){
    vAbsCsv.push_back(AiNpcPlayerCsv::getInstance());
    vAbsCsv.push_back(AntiFraudCsv::getInstance());
    vAbsCsv.push_back(ArchitecturalBlueprintCsv::getInstance());
    vAbsCsv.push_back(AvatarDefCsv::getInstance());
    vAbsCsv.push_back(BiomeGroupDefCsv::getInstance());
    vAbsCsv.push_back(BlockDefCsv::getInstance());
    vAbsCsv.push_back(BlockEffectDefCsv::getInstance());
    vAbsCsv.push_back(BotConversationsDefCsv::getInstance());
    vAbsCsv.push_back(BuffDefCsv::getInstance());
    vAbsCsv.push_back(BuffEffectBankCsv::getInstance());
    vAbsCsv.push_back(BuffEffectEnumCsv::getInstance());
    vAbsCsv.push_back(BuffEffectSlidingCsv::getInstance());
    vAbsCsv.push_back(BuildingMaintenanceCsv::getInstance());
    vAbsCsv.push_back(ChestSpawnCsv::getInstance());
    vAbsCsv.push_back(ColorMixDefCsv::getInstance());
    vAbsCsv.push_back(CreateRoleAvatarCsv::getInstance());
    vAbsCsv.push_back(CustomGunDefCsv::getInstance());
    vAbsCsv.push_back(CustomPrefabInfoCsv::getInstance());
    vAbsCsv.push_back(DieInfoCsv::getInstance());
    vAbsCsv.push_back(DisassembleCsv::getInstance());
    vAbsCsv.push_back(EquipDefCsv::getInstance());
    vAbsCsv.push_back(EquipGroupDefCsv::getInstance());
    vAbsCsv.push_back(FishingDefCsv::getInstance());
    vAbsCsv.push_back(FoodDefCsv::getInstance());
    vAbsCsv.push_back(GameLanguageCsv::getInstance());
    vAbsCsv.push_back(GameZoneCsv::getInstance());
    vAbsCsv.push_back(HorseEggCsv::getInstance());
    vAbsCsv.push_back(ItemDefCsv::getInstance());
    vAbsCsv.push_back(ItemInHandDefCsv::getInstance());
    vAbsCsv.push_back(ItemSkillDefCsv::getInstance());
    vAbsCsv.push_back(ItemStatusDefCsv::getInstance());
    vAbsCsv.push_back(LuaDefCsv::getInstance());
    vAbsCsv.push_back(MergeMobileSupportCsv::getInstance());
    vAbsCsv.push_back(MonsterCsv::getInstance());
    vAbsCsv.push_back(OperateUIDataCsv::getInstance());
    vAbsCsv.push_back(ParticlesCsv::getInstance());
    vAbsCsv.push_back(ParticlesStrDefCsv::getInstance());
    vAbsCsv.push_back(PhysicsActorCsv::getInstance());
    vAbsCsv.push_back(PlayerAttribCsv::getInstance());
    vAbsCsv.push_back(ProjectileDefCsv::getInstance());
    vAbsCsv.push_back(ResourcePackDefCsv::getInstance());
    vAbsCsv.push_back(RoleSkinCsv::getInstance());
    vAbsCsv.push_back(RuleOptionCsv::getInstance());
    vAbsCsv.push_back(ScoreCsv::getInstance());
    vAbsCsv.push_back(SkinActCsv::getInstance());
    vAbsCsv.push_back(SkinningToolCsv::getInstance());
    vAbsCsv.push_back(SoundStrDefCsv::getInstance());
    vAbsCsv.push_back(SprayPaintDefCsv::getInstance());
    vAbsCsv.push_back(StringDefCsv::getInstance());
    vAbsCsv.push_back(SummonDefCsv::getInstance());
    vAbsCsv.push_back(SurviveObjectiveDefCsv::getInstance());
    vAbsCsv.push_back(SurviveTaskDefCsv::getInstance());
    vAbsCsv.push_back(ToolDefCsv::getInstance());
    vAbsCsv.push_back(UgcMaterialCsv::getInstance());
    vAbsCsv.push_back(UgcModelCsv::getInstance());
    vAbsCsv.push_back(WorkbenchTechCsv::getInstance());
}
#endif//__CsvAutoInclude_h__
