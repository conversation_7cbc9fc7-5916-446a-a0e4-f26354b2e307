#include "AINpc.h"
#include "PlayerLocoMotion.h"
#include "Platforms/PlatformInterface.h"
//#include "defmanager.h"
#include "defdata.h"
//#include "RoleSkinCsv.h"
#include "PlayerLocoMotion.h"
#include "backpack.h"
#include "ICloudProxy.h"
#include "CameraModel.h"
#include "GameNetManager.h"
#include "PlayerStateController.h"
#include "ClientActorFuncWrapper.h"
#include "PlayerAttrib.h"
#include "ActorVision.h"
#include "ActorBody.h"
#include "AIPlayerDigBlock.h"
#include "AIPlayerPosWander.h"
#include "AIPlayerAtk.h"
#include "AIPlayerTargetHurtee.h"
#include "AIPlayerFear.h"
#include "Core/blocks/container_world.h"
#include "Utils/luaConstProxy/LuaInterfaceProxy.h"
#include "Common/OgreTimer.h"

#include <random>
/**************************** AINpcLocoMotion start*******************************************/
class AINpcLocoMotion: public PlayerLocoMotion
{
	DECLARE_COMPONENTCLASS(AINpcLocoMotion)
    typedef PlayerLocoMotion _Super;
    typedef LivingLocoMotion _Grand;

	int m_AINPCMoveStoping;
	WCoord m_AINPCMoveStart;
	unsigned m_AINPCMoveDistance;
protected:
	virtual void moveEntityWithHeading(float strafing, float forward) override;
	virtual float getMoveReduceRatio() const override {return m_AINPCMoveStoping > 0? 0.95: 0.98;}
public:
	void setAINPCMoveTarget(const WCoord &target)
	{
		m_AINPCMoveStart = getPosition();
		m_AINPCMoveDistance = m_AINPCMoveStart.distanceTo(target);
		m_AINPCMoveStoping = 0;
	};
	virtual void tick() override;
};

IMPLEMENT_COMPONENTCLASS(AINpcLocoMotion)
void AINpcLocoMotion::tick()
{
    _Super::tick();
	if (m_AINPCMoveStoping > 0)
		-- m_AINPCMoveStoping;
	if (getPosition().distanceTo(m_AINPCMoveStart) > m_AINPCMoveDistance)
	{
		m_AINPCMoveStoping = 3;
		clearTarget();
		m_MoveStrafing = 0;
		m_MoveForward = 0;
		m_isJumping = false;
	}
}
void AINpcLocoMotion::moveEntityWithHeading(float strafing, float forward)
{
    _Grand::moveEntityWithHeading(strafing, forward);
}

/***********************************************************/
IMPLEMENT_SCENEOBJECTCLASS(AINpcPlayer)
void AINpcPlayer::setAINPCMoveTarget(const WCoord &target)
{
	static_cast<AINpcLocoMotion *>(getLocoMotion())->setAINPCMoveTarget(target);
}

void AINpcPlayer::tick()
{
    _Super::tick();
    keepBreath();
	m_AITask->onUpdateTasks();
	m_AITaskTarget->onUpdateTasks();
}
void AINpcPlayer::keepBreath()
{
    int now = Rainbow::Timer::getSystemTick();
    int uin = getObjId();
    if(GameNetManager::getInstance()->m_HostRecvHeartBeart.count(uin) == 0)
    {
        m_LastBreathTime = 0;
    }
    if (now - m_LastBreathTime > 20 * 1000)
    {
        m_LastBreathTime = now;
        GetGameNetManagerPtr()->m_HostRecvHeartBeart[getObjId()] = now;
    }
}


#define MAX_TAMEDMON_FOLLOWS_LENGTH 5

#define DEFAULT_BOUND_WIDTH 60
#define DEFAULT_BOUND_HEIGHT 180
#define DEFAULT_HIT_HEIGHT 220
#define DEFAULT_HIT_WIDTH 120
#define DEFAULT_HIT_THICKNESS 120

bool AINpcPlayer::init(int uin, const char *nickname, int playerindex, const char *customjson)
{

	m_Def = new MonsterDef();

	CreateComponent<ActorVision>("ActorVision");


	if (playerindex <= 0)
		playerindex = 1;

	m_ObjId = uin;
	m_Nickname = nickname;
	GetDefManagerProxy()->filterStringDirect((char*)m_Nickname.c_str());

	m_AITask = ENG_NEW(AINpcPlayerTask)(this);
	m_AITaskTarget = ENG_NEW(AINpcPlayerTask)(this);

	// 初始化Home和Trace相关变量
	m_HomeDist = -1;
	m_TraceDist = -1;
	m_isNeedRetHome = false;

	m_Panic = false;
	m_bNeedRunAway = false;

	// 初始化SpawnPoint
	m_SpawnPoint = WCoord::zero;
	
	// 初始化个人AINPC相关变量
	m_isPersonalAINPC = false;
	m_noPlayerNearbyTimer = 0;

	if (customjson)
		m_strCustomjson = customjson;
	else
		m_strCustomjson.clear();

	m_strOriginCustomJson = m_strCustomjson;

	ENG_DELETE(m_Body);

	m_Body = ENG_NEW(ActorBody)(this);

	getBody()->initPlayer(playerindex, 0, customjson);

	m_StateController = ENG_NEW(PlayerStateController)();
	m_StateController->init();
	m_StateController->setClientPlayer(this);

	m_originSkinId = getBody()->getSkinID();
	AINpcLocoMotion* loco = CreateComponent<AINpcLocoMotion>("AINpcLocoMotion");

	auto functionWrapper = getFuncWrapper();
	if (functionWrapper)
	{
		std::stringstream str;
		if (getBody()->getSkinID() > 0)
		{
			str << "skin_" << getBody()->getSkinID();
			functionWrapper->setActorFacade(str.str());
		}
		else if (getBody()->getModelID() > 0)
		{
			str << "role_" << getBody()->getModelID();
			functionWrapper->setActorFacade(str.str());
		}
	}

#ifndef IWORLD_SERVER_BUILD
	const RoleDef* def = GetDefManagerProxy()->getRoleDef(getBody()->getModelID(), getBody()->getGeniusLv());
#else
	const RoleDef *def = GetDefManagerProxy()->getRoleDef(MNSandbox::PlayerIndex2Model(playerindex), MNSandbox::PlayerIndex2Genius(playerindex)); ;
#endif 
	if (def != NULL)
	{
		//getLocoMotion()->setAttackBound(def->HitHeight, def->HitWidth, def->HitThickness);
		updateAttackBound(def->HitHeight, def->HitWidth, def->HitThickness);
	}

	//getLocoMotion()->setBound(180, 60);
	updateBound(180, 60);

	auto attrib = CreateComponent<PlayerAttrib>("PlayerAttrib",this);
	do //绑定效果事件
	{
		auto callBackAppend = [this](int buffid, int bufflvl) {
			this->onBuffAppend(buffid, bufflvl);
		};
		attrib->setDelegateBuffAppend(callBackAppend);

		auto callBackRemove = [this](int buffid, int bufflvl) {
			this->onBuffRemove(buffid, bufflvl);
		};
		attrib->setDelegateBuffRemove(callBackRemove);

	} while (false);
	//	m_Attrib = attrib;
	m_PlayerAttrib = static_cast<PlayerAttrib*>(attrib);

	ChangeNameObjHeight();

	initAIComponent();

	generateRandomBackpack();
	
	generateRandomEquipment();

	PackContainer* shortcut_pack = getBackPack()->getPack(getShortcutStartIndex());
	if (shortcut_pack) {
		shortcut_pack->addItemByCount(2426, 1);  //火把
		shortcut_pack->addItemByCount(3030208,1); //岩石
		shortcut_pack->addItemByCount(3030206, 1); //石镐
		setCurrentShortCutItem(3030206);
	}

	return true;
}

class ItemSearcher: public GridVisitor
{
	int m_FoundIndex;
	int m_ItemID;
public:
	ItemSearcher(int item_id):m_ItemID(item_id), m_FoundIndex(-1){}
	void setItemID(int item_id) { m_ItemID = item_id; m_FoundIndex = -1;}
	void reset() { m_FoundIndex = -1;}
	int found(){return m_FoundIndex;}
	virtual void visit(const BackPackGrid * grid)
	{
		if (m_FoundIndex != -1)
			return;
		if (!grid)
			return;
		if (grid->getItemID() == m_ItemID)
            m_FoundIndex = grid->getIndex();
	}
};
bool AINpcPlayer::setCurrentShortCutItem(int item_id)
{
	if (item_id < 0)
        return false;

    if (getCurToolID() == item_id)
    {
        return true;
    }
	ItemSearcher vistor(item_id);
	PackContainer* shortcut_pack =  getBackPack()->getPack(getShortcutStartIndex());  
	if (!shortcut_pack)
		return false;
	
	shortcut_pack->visitPack(&vistor);
	int found_index = vistor.found();
	if (found_index!= -1)
	{
		onSetCurShortcut(found_index - getShortcutStartIndex());
		return true;
	}

	PackContainer* backpack =  getBackPack()->getPack(BACKPACK_START_INDEX);
	if (backpack)
		backpack->visitPack(&vistor);
	
	int backpack_gun_index = vistor.found();
	if (backpack_gun_index != -1)
	{
		int to_shortcut_index = getShortcutStartIndex();
		vistor.setItemID(0);
		shortcut_pack->visitPack(&vistor);
		if (vistor.found()!= -1)
		{
			to_shortcut_index = vistor.found();
		}
		getBackPack()->swapItem(backpack_gun_index, to_shortcut_index);

        onSetCurShortcut(to_shortcut_index - getShortcutStartIndex());
        return true;
    }

    return false;
}

// Home和Trace相关方法实现 (移植自ClientMob)
void AINpcPlayer::setHome(int iHomeDist, int x, int y, int z)
{
	m_HomeDist = iHomeDist;
	getLocoMotion()->setHomePosition(WCoord(x, y, z));
}

bool AINpcPlayer::isInHomeDist(int x, int y, int z)
{
	if (m_HomeDist == -1) return true;
	WCoord pos(x, y, z);
	WCoord vec = getLocoMotion()->getHomePosition() - pos;

	if (vec.length() > m_HomeDist) return false;
	else return true;
}

// SpawnPoint相关方法实现 (移植自ClientMob)
void AINpcPlayer::setSpawnPoint(const WCoord& spawnPos)
{
	this->getActorType();
	m_SpawnPoint = spawnPos;
	getLocoMotion()->setPosition(spawnPos.x* BLOCK_SIZE, spawnPos.y * BLOCK_SIZE + BLOCK_SIZE, spawnPos.z * BLOCK_SIZE);
}


void AINpcPlayer::initAIComponent() {
		
	addAiTask<AIPlayerFear>(1, 1.5, 4*100, 20*5, 10);  //害怕玩家，当玩家靠近时会逃跑。 setMutexBits(3);
	addAiTask<AIPlayerAtk>(2, 0, 1);  //反击 setMutexBits(3);
	addAiTask<AIPlayerDigBlock>(3, 10, 10 * 100, 123, 1, 3 * 100);//挖掘 捡掉落物 setMutexBits(7);
	addAiTask<AIPlayerPosWander>(5,10, 0, 0, 30, 1); //闲逛	setMutexBits(1);
	
	addAiTaskTarget<AIPlayerTargetHurtee>(1); // 锁定目标
	
}

// AINPC背包生成系统实现

void AINpcPlayer::generateRandomBackpack()
{
	int chestType = selectRandomChestType();
	if (chestType == 0) {
		LOG_WARNING("AINPC %d generateRandomBackpack failed", getUin());
		return;
	}
	generateBackpackByType(chestType);
}

void AINpcPlayer::generateBackpackByType(int chestType)
{
	// 创建随机数生成器
	ChunkRandGen randgen;
	randgen.setSeed(Rainbow::Timer::getSystemTick() + getUin());
	
	// 生成物品列表
	std::vector<GenerateItemDesc> items;
	WorldContainerMgr::generateChestItems(items, chestType, &randgen, 0);
	
	// 获取背包
	PackContainer* backpack = getBackPack()->getPack(getShortcutStartIndex());
	if (!backpack) {

		return;
	}
	
	for (const auto& item : items) {
		if (item.itemid > 0 && item.itemnum > 0) {
			backpack->addItemByCount(item.itemid, item.itemnum);
			LOG_WARNING("AINPC背包生成: %d %d", item.itemid, item.itemnum);
		}
	}
}

int AINpcPlayer::selectRandomChestType()
{
	const auto& AINPCCfgs = GetLuaInterfaceProxy().get_lua_const()->AINPCCfgs;
	if (AINPCCfgs.empty()) {
		return 0;
	}

	static std::random_device rd;
	static std::mt19937 gen(rd());
	std::uniform_int_distribution<int> dis(0, AINPCCfgs.size() - 1);
	
	return AINPCCfgs[dis(gen)].backpack;
}

// AINPC装备生成系统实现
void AINpcPlayer::generateRandomEquipment()
{
	int chestType = selectRandomEquipmentChestType();
	if (chestType == 0) {
		LOG_WARNING("AINPC %d generateRandomEquipment failed", getUin());
		return;
	}
	
	generateEquipmentByType(chestType);
}

void AINpcPlayer::generateEquipmentByType(int chestType)
{
	ChunkRandGen randgen;
	randgen.setSeed(Rainbow::Timer::getSystemTick() + getUin() + chestType);
	
	std::vector<GenerateItemDesc> items;
	WorldContainerMgr::generateChestItems(items, chestType, &randgen, 0);
	
	for (const auto& item : items) {
		if (item.itemid > 0) {
			// 根据物品ID确定装备槽位
			EQUIP_SLOT_TYPE slotType = getEquipSlotTypeByItemId(item.itemid);
			if (slotType != EQUIP_NONE) {
				// 使用PlayerAttrib的equip方法来装备物品
				PlayerAttrib* attrib = getPlayerAttrib();
				if (attrib) {
					const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(item.itemid);
					int durability = toolDef ? toolDef->Duration : 100;
					
					attrib->equip(slotType, item.itemid, durability, 0, durability);
					LOG_WARNING("AINPC装备生成: 槽位=%d, 物品ID=%d, 数量=%d", (int)slotType, item.itemid, item.itemnum);
				}
			}
		}
	}
}

int AINpcPlayer::selectRandomEquipmentChestType()
{
	const auto& AINPCCfgs = GetLuaInterfaceProxy().get_lua_const()->AINPCCfgs;
	if (AINPCCfgs.empty()) {
		return 0;
	}
	
	static std::random_device rd;
	static std::mt19937 gen(rd());
	std::uniform_int_distribution<int> dis(0, AINPCCfgs.size() - 1);
	
	return AINPCCfgs[dis(gen)].equip;
}

EQUIP_SLOT_TYPE AINpcPlayer::getEquipSlotTypeByItemId(int itemId)
{
	// 根据ToolDef获取装备槽位类型
	const ToolDef* toolDef = GetDefManagerProxy()->getToolDef(itemId);
	if (toolDef) {
		return toolDef->getSlotType();
	}
	return EQUIP_NONE;
}

// AINPC PlayerBriefInfo初始化实现
void AINpcPlayer::initializePlayerBriefInfo(PlayerBriefInfo* info, int uin, const std::string& name, const WCoord& spawnPos)
{
	if (!info) return;
	
	// 设置AINPC名字到PlayerBriefInfo
	snprintf(info->nickname, sizeof(info->nickname), "%s", name.c_str());
	
	// 标记为AI NPC
	info->isAINpc = true;
	
	// 设置基础属性（参考PlayerAttrib默认值）
	info->model = 0;           // 默认模型
	info->geniuslv = 0;        // 默认天赋等级
	info->skinid = 0;          // 默认皮肤ID
	info->hp = 100.0f;         // 默认生命值
	info->maxhp = 100.0f;      // 最大生命值
	info->strength = 100.0f;   // 默认体力值
	info->maxstrength = 100.0f; // 最大体力值
	info->overflowhp = 0.0f;   // 溢出生命值
	info->overflowstrength = 0.0f; // 溢出体力值
	info->foodlevel = 100.0f;  // 饱食度
	info->thirst = 100.0f;     // 口渴值
	info->maxthirst = 100.0f;  // 最大口渴值
	info->overflowthirst = 0.0f; // 溢出口渴值
	info->armor = 0.0f;        // 护甲值
	info->perseverance = 0.0f; // 坚韧值
	
	// 设置位置信息（生成位置）
	info->x = spawnPos.x;
	info->y = spawnPos.y;
	info->z = spawnPos.z;
	info->mapid = 0;           // 默认地图ID
	info->teamid = 0;          // 默认队伍ID
	
	// 设置其他字段
	info->frameid = 0;         // 默认头像框ID
	info->accountSkinID = 0;   // 账户皮肤ID
	info->UICtrlMode = 0;      // UI控制模式
	info->lang = 0;            // 语言设置
	info->apiid = 999;         // API ID
	info->inSpectator = 0;     // 非观察者模式
	info->exposePosToOther = true; // 允许暴露位置给其他玩家
	
	// 设置进入时间和顺序
	info->enterTime = Rainbow::Timer::getSystemTick();
	info->setOrder();
	
	// 清空字符串字段
	memset(info->bptitle, 0, sizeof(info->bptitle));
	memset(info->customjson, 0, sizeof(info->customjson));
	memset(info->auth, 0, sizeof(info->auth));
	memset(info->game_session_id, 0, sizeof(info->game_session_id));
	memset(info->country, 0, sizeof(info->country));
	
	// 清空游戏变量
	memset(info->cgamevar, 0, sizeof(info->cgamevar));
}
