#ifndef IWORLD_SERVER_BUILD 

#include "GameAnalytics.h"

#include <iostream>
#include <type_traits>

#include "RapidJSON/RapidJSONConfig.h"
#include "RapidJSON/document.h"

#ifdef USE_THINKINGDATA
using namespace thinkingdata;
#endif

// 全局变量定义
GameAnalytics* g_pGameAnalytics = nullptr;

GameAnalytics::CommonProperties GameAnalytics::s_commonProps;
bool GameAnalytics::m_initialized = false;

// 构造函数和析构函数实现
GameAnalytics::GameAnalytics() {
  // 构造函数实现
}

GameAnalytics::~GameAnalytics() {
  // 析构函数实现
}

// 初始化方法实现
bool GameAnalytics::Init(const std::string& device_id, int env) {
    bool success = false;
#ifdef USE_THINKINGDATA
    // 如果全局变量已经初始化，直接返回true
    if (g_pGameAnalytics != nullptr) {
        return true;
    }
    
    // 创建全局对象
    g_pGameAnalytics = new GameAnalytics();
    
    // 初始化埋点SDK
    std::string appid = "a12c62532cf54941ba8cb3cb63784b07";  
    if (env == 1) {
        appid = "199b3326948e4dde9dd338bcc3634c6e";//测试
    }
    std::string server_url = "https://tga.mini1.cn";
    bool is_login_id = false; // device_id 不是登录ID
    int max_staging_record_count = 10000; // 最大暂存记录数

    // 配置ThinkingData SDK
    TDConfig td_config;
    td_config.appid = appid;
    td_config.server_url = server_url;
    td_config.enableAutoCalibrated = true; // 自动时间校准
    td_config.mode = TDMode::TD_NORMAL;
    td_config.databaseLimit = max_staging_record_count;
    td_config.dataExpression = 15;

    success = ThinkingAnalyticsAPI::Init(td_config);
    
    if (success) {
        m_initialized = true;
        s_commonProps.env = env;                // 设置环境
        s_commonProps.device_id = device_id;    // 设置设备ID
        s_commonProps.log_id = genLogid();
        ThinkingAnalyticsAPI::Identify(device_id); //使用device_id作为访客id
    }

    ThinkingAnalyticsAPI::EnableLog(true);
#endif
    return success;
}

std::string GameAnalytics::GetDistinctID()
{
#ifdef USE_THINKINGDATA
    return ThinkingAnalyticsAPI::DistinctID();
#endif
}


void GameAnalytics::InitCommonProps(CommonProperties& properties){
    s_commonProps.apiid = properties.apiid;
    s_commonProps.app_version = properties.app_version;
    s_commonProps.env = properties.env;
    GameAnalytics::SetSessionInfo(properties.session_id);
    s_commonProps.log_id = properties.session_id;
    s_commonProps.country = properties.country;
    s_commonProps.province = properties.province;
    s_commonProps.channel = properties.channel;
    s_commonProps.app_language = properties.app_language;
    
    GameAnalytics::SetDeviceInfo(properties.ip_address, properties.os_type, properties.network_type);

    //WarningString("[GameAnalytics] InitCommonProps success");
    
}

 

// 设置会话相关信息
void GameAnalytics::SetSessionInfo(const std::string& session_id ) {
    s_commonProps.session_id = session_id;
    s_commonProps.session_start_time = time(0);
}

// 设置游戏会话信息
void GameAnalytics::SetGameSessionInfo(const std::string& game_session_id) {
    s_commonProps.game_session_id = game_session_id;
    s_commonProps.game_session_start_time = time(0);
}

void GameAnalytics::SetGameServerInfo(CommonProperties& properties) {
    s_commonProps.roomid = properties.roomid;
    s_commonProps.mapid = properties.mapid;
    s_commonProps.host = properties.host; //ip:port
    s_commonProps.mapver = properties.mapver;
    s_commonProps.area = properties.area;

}

// 设置设备信息
void GameAnalytics::SetDeviceInfo(const std::string& ip_address, const std::string& os_type, int apn) {
    s_commonProps.ip_address = ip_address;
    s_commonProps.apn = apn;
    s_commonProps.os_type = os_type;
}

 

void GameAnalytics::Login(const std::string& login_id) {
#ifdef USE_THINKINGDATA
    ThinkingAnalyticsAPI::Login(login_id);
    s_commonProps.uin = login_id;
#endif
}

void GameAnalytics::Logout() {
#ifdef USE_THINKINGDATA
    m_initialized = false;
    // 调用SDK登出
    ThinkingAnalyticsAPI::LogOut();
#endif
}

void GameAnalytics::TrackEvent(const std::string& event_name, std::map<std::string, Value> data){
#ifdef USE_THINKINGDATA
    if (!m_initialized) {
       // WarningString("[GameAnalytics] Not initialized");
        return;
    }

    if (event_name.empty()) {
        return;
    }
    //WarningStringMsg("[GameAnalytics] TrackEvent event_name=%s",event_name.c_str());
    thinkingdata::TDJSONObject properties = createCommonProperties();
    
    for (auto it = data.begin(); it != data.end(); it++) {
        std::string key = it->first;
        Value value = it->second;
        
        switch (value.type) {
            case Value::Type::String:
                properties.SetString(key, value.string_value);
                break;
            case Value::Type::Int:
                properties.SetNumber(key, value.int_value);
                break;
            case Value::Type::Float:
                properties.SetNumber(key, value.float_value);
                break;
            case Value::Type::Bool:
                properties.SetBool(key, value.bool_value);
                break;
        }
    }
    
    ThinkingAnalyticsAPI::Track(event_name, properties);
    ThinkingAnalyticsAPI::Flush();
#endif
}

void GameAnalytics::TrackEvent(const std::string& event_name, const std::string& json_data ) {
#ifdef USE_THINKINGDATA
    if (!m_initialized) {
        //WarningString("[GameAnalytics] Not initialized");
        return;
    }
    std::string jsondata = json_data;
    if (event_name.empty()) {
        return;
    }
   
    //WarningStringMsg("[GameAnalytics] TrackEvent event_name=%s jsondata=%s", event_name.c_str(), jsondata.c_str());

    if (jsondata.empty()) {
        ThinkingAnalyticsAPI::Track(event_name);
        ThinkingAnalyticsAPI::Flush();
        return;
    }

    // 解析json
    Rainbow::rapidjson::Document  root;
    root.Parse(jsondata.c_str());

    if (!root.IsObject()) {
        //WarningString("[GameAnalytics] Failed to parse JSON");
        return;
    }
    
    thinkingdata::TDJSONObject properties = createCommonProperties();
    // 遍历 rapidjson json
    for (auto it = root.MemberBegin(); it != root.MemberEnd(); ++it) {
        const Rainbow::rapidjson::Value& value = it->value;
        if (value.IsString()) {
            std::string value_str = value.GetString();
            properties.SetString(it->name.GetString(), value_str);
        } else if (value.IsInt()) {
            int value_int = value.GetInt();
            properties.SetNumber(it->name.GetString(), value_int);
        } else if (value.IsDouble()) {
            float value_float = value.GetDouble();
            properties.SetNumber(it->name.GetString(), value_float);
        } else if (value.IsBool()) {
            bool value_bool = value.GetBool();
            properties.SetBool(it->name.GetString(), value_bool);
        }
    }

    ThinkingAnalyticsAPI::Track(event_name, properties);
    ThinkingAnalyticsAPI::Flush();
#endif
}

#ifdef USE_THINKINGDATA
thinkingdata::TDJSONObject GameAnalytics::createCommonProperties() {
    thinkingdata::TDJSONObject properties;

    /*
        session_id	会话ID	字符串	
        is_first_session	是否首次启动会话	布尔	安装后首次启动为true
        channel_id	渠道ID	数值	
        app_version	APP版本	字符串	
        ip	IP地址	字符串	
        os_type	系统类型	数值	全小写
        os_version	系统版本	字符串	全小写
        os_language	系统语言	字符串	全小写
        network_type	网络状态	数值	0=无网络连接，1=wifi，2/3/4/5=2/3/4/5G
        country	国家地区	文本	用户所在国家或地区，根据 IP 地址生成
        country_code	国家地区代码	文本	用户所在国家或地区的国家地区代码(ISO 3166-1 alpha-2，即两位大写英文字母)，根据 IP 地址生成
        timezone_offset	时区偏移	数值	数据时间相对 UTC 时间的偏移小时数
        app_language	APP语言	字符串	全小写
        fps	FPS	数值	
        ping	PING	数值	
        game_id	游戏会话ID	字符串	
    */

    properties.SetString("session_id", s_commonProps.session_id);
    properties.SetNumber("env", s_commonProps.env);
    properties.SetString("ip", s_commonProps.ip_address);
    properties.SetString("app_version", s_commonProps.app_version);
    properties.SetNumber("apiid", s_commonProps.apiid);
    properties.SetNumber("channel_id", s_commonProps.channel);
    properties.SetString("os_type", s_commonProps.os_type);
    properties.SetString("os_version", "");
    properties.SetString("os_language", "");
    properties.SetString("app_language", s_commonProps.app_language);

    properties.SetNumber("network_type", s_commonProps.apn);
    properties.SetString("country", s_commonProps.country);
    properties.SetString("country_code", s_commonProps.country);
    properties.SetNumber("timezone_offset", 0);
    
    properties.SetNumber("fps", 0);
    properties.SetNumber("ping", 0);
    properties.SetString("game_id", s_commonProps.game_session_id);

    properties.SetString("device_id", s_commonProps.device_id);
    properties.SetString("uin", s_commonProps.uin);
    

    return properties;
}
#endif


// 模板函数实现
template<typename T>
void GameAnalytics::SetUserProfile(const std::string& property_name, const T& value) {
#ifdef USE_THINKINGDATA
    TDJSONObject userProps;
    if constexpr (std::is_same<T, std::string>::value) {
        userProps.SetString(property_name, value);
    } else if constexpr (std::is_same<T, int>::value) {
        userProps.SetNumber(property_name, value);
    } else if constexpr (std::is_same<T, bool>::value) {
        userProps.SetBool(property_name, value);
    } else if constexpr (std::is_same<T, double>::value || std::is_same<T, float>::value) {
        userProps.SetNumber(property_name, static_cast<double>(value));
    }
    ThinkingAnalyticsAPI::UserSet(userProps);
#endif
}

// 显式实例化常用类型
template void GameAnalytics::SetUserProfile<std::string>(const std::string&, const std::string&);
template void GameAnalytics::SetUserProfile<int>(const std::string&, const int&);
template void GameAnalytics::SetUserProfile<bool>(const std::string&, const bool&);
template void GameAnalytics::SetUserProfile<double>(const std::string&, const double&);
template void GameAnalytics::SetUserProfile<float>(const std::string&, const float&);


std::string GameAnalytics::genLogid() {
    return "";
}

int GameAnalytics::getSessionDuration() {
    return  time(0) - s_commonProps.session_start_time;
}

int  GameAnalytics::getGameSessionDuration() {
    return  time(0) - s_commonProps.game_session_start_time;

}

void GameAnalytics::Tick(double t) {}

#endif
