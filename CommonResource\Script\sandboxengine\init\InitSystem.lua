if not SandboxClass then assert(false) return end

local function onInit(context)
    local paramData = context:GetParamData()
    LuaGameRuntime:setGameState(LuaGameRuntime.GameState.GameState_Init)
end

local function PreTick(context)
    local paramData = context:GetParamData()
end

local function Tick(context)
    local paramData = context:GetParamData()
end

local function FrameUpdate(context)
    local paramData = context:GetParamData()
end

local function TickEnd(context)
    local paramData = context:GetParamData()
end

local function onStart(context)
    local paramData = context:GetParamData()
end

local function onStop(context)
    local paramData = context:GetParamData()
end

local function onStoping(context)
    local paramData = context:GetParamData()
end

local function onPause(context)
    local paramData = context:GetParamData()
end

local function onResume(context)
    local paramData = context:GetParamData()
end

local function onGameStateChanged(context)
    local paramData = context:GetParamData()
end

local function onUninit(context)
    local paramData = context:GetParamData()
end

--[[
    游戏数据初始化Lua封装
]]
local InitSystem = SandboxClass:Class("InitSystem", "SandboxLuaObject")

InitSystem.constructor = function(self)
    self._super.constructor(self) -- 父类
    SandboxLua.eventDispatcher:SubscribeEvent(nil, "Init", self._sandboxobj, onInit)
 
end

InitSystem.destructor = function(self)
end

PlayerSSDevGoods = {}
function AddTempPlayerSSDevGoods(uin, itemId)
    PlayerSSDevGoods[uin] = itemId
end

function OnClientTakeSSDevGoods(content)
    if WorldMgr and content and content.uin then
        local player = WorldMgr:GetPlayerByUin(content.uin)
        if player and PlayerSSDevGoods[content.uin] then
            player:gainItems(PlayerSSDevGoods[content.uin], 1)
        end
    end
end


function OnClientTakeSSNpcShopGoods(content)
    if WorldMgr and content and content.uin and content.itemid then
        
        local shopid = GetPlayerNpcInteract(content.uin)
        -- 校验有无收到客机发送NPC交互请求
        if not shopid  then
            return
        end
        local pNpcShopDef = DefMgr:getNpcShopDef(shopid)
        if not pNpcShopDef  then
            return
        end
        -- 主机校验有无这个商品
        local hasgoods = false
        local size = pNpcShopDef:getSkuSize()
        for i=1,size do
            local skuinfo = pNpcShopDef:getNpcShopSkuDefByIdx(i-1)
            if skuinfo.iItemID == content.itemid and skuinfo.iShowAD == 1 then
                hasgoods = true
                break
            end
        end
        if hasgoods then
            local player = WorldMgr:GetPlayerByUin(content.uin)
            if player then
                player:gainItems(content.itemid, 1)
            end
        end
    end
end

function SubscribeLuaMsgs()
    SandboxLuaMsg:SubscibeMsgHandle("get_dev_goods", OnClientTakeSSDevGoods)
    SandboxLuaMsg:SubscibeMsgHandle("get_npcshop_goods", OnClientTakeSSNpcShopGoods)
    -- 收主机视频时间
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.UGC.CONST_NET_EVENT_H2C_RSP_VIDEO_TIME, function(...)
		GetInst("DisplayBoardNetManager"):Host2ClientVideoTime(...)
	end)
    -- 收客机的视频时间请求
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.UGC.CONST_NET_EVENT_C2H_REQ_VIDEO_TIME, function(...)
		GetInst("DisplayBoardNetManager"):Client2HostVideoTime(...)
	end)

    LuaMsgHandler:SubscribeLuaMsg()
end

InitSystem.InitGameData = function(self)
    MiniLogWarning("LSJ lua InitGameData start 1")
    -- local t1 = CommonUtil:getSystemTick()
    -- MiniLog("InitGameData tick="..t1)
    GetClientInfo():setSandboxEngineState(GAME_RES_LOADING)
    GetClientApp():InitPhysXMgr()
    GetClientApp():InitTriggerScriptMgr()
    GetClientApp():SetUserTypePointer()
    SubscribeLuaMsgs()
    MiniLogWarning("LSJ lua InitGameData start 2")
    
    if HttpFileUpDownMgr then
        HttpFileUpDownMgr:setProgressCallback()
    end

    if PlatformUtility:isPC() and MicroUpdateMgr then
        MicroUpdateMgr:initMicroUpdate()
    end

    MiniLogWarning("LSJ lua InitGameData start 3")
    if PlatformUtility:isPureServer() then
        RoomManager:setSeverProxyOwindex(tonumber(GetClientInfo():getEnterParam("owindex")))
        -- print("jackdebug: __cloudserver_hotfix__")
        --__cloudserver_hotfix__()    
    end

    if SdkManager then
        SdkManager:createBodyAR(GetClientInfo():GetAppId())
    end

    MiniLogWarning("LSJ lua InitGameData start 4")
    if gFunc_IsExpObjTool() then
        GetClientInfo():setKeepOriginImage(true)
    end

    require("luascript.ugc.ugc_init")
    if not _G.IsServerBuild then
        require("sandboxengine.toc.uistages_init")
    end
    MiniLogWarning("LSJ lua InitGameData start 5")
    -- GetClientApp():LoadScriptTOC("luascript/scriptsupport/scriptsupport.toc")
    -- GetClientApp():LoadScriptTOC("sandboxengine/toc/uistages.toc") --跳转管理相关lua
    -- local t2 = CommonUtil:getSystemTick()
    -- MiniLog("InitGameData before initGameUI cost time =" .. (t2 - t1))
    if LuaGameRuntime:getNeedShowGameUI() then
        if GetClientInfo():isEditorMode() then
            self:InitGameUI("editor/luascript/game_start_editor_init.lua")
        else
            self:InitGameUI("ui/mobile/mvc/commonComp/game_start_init.lua")
        end
    end
    MiniLogWarning("LSJ lua InitGameData start 6")
    -- 服务器单独加载下server_afterusertype_init 此处usertype等已经初始化完成
    if _G.IsServerBuild then
        -- 2024-9-6 16:49:17 本地云服：≈55ms
        require("luascript.server_afterusertype_init.lua")
    end

	-- 2024-9-6 16:49:17 本地云服：≈20~35ms
    require("sandboxengine.toc.gameCommon_init")
    MiniLogWarning("LSJ lua InitGameData start 7")
    -- GetClientApp():LoadScriptTOC("sandboxengine/toc/game_plugin.toc") --sandboxengine plugin相关
    if not _G.IsServerBuild then
        LoginFrame_OnLoad()
    end
    MiniLogWarning("LSJ lua InitGameData start 8")
    -- 本意是为了处理与热更场景的fui资源释放冲突，但是会闪屏，体验不好，所以改成处理热更场景的fui资源不释放了 code_by:huangfubin 2024.8.16
    -- threadpool:delay(0.1,
    --     function()
        -- if not GetClientInfo():isEditorMode() then
        --     if LoginFrame_OnLoad then
        --         LoginFrame_OnLoad()
        --     end
        -- end
    --     end
    -- )

    -- local t3 = CommonUtil:getSystemTick()
    -- MiniLog("InitGameData initGameUI cost time =" .. (t3- t2))
    GetClientApp():InitGameData()
    GetClientInfo():setSandboxEngineState(GAME_WAITING)
    MiniLogWarning("LSJ lua InitGameData start 9")
    -- local t4 = CommonUtil:getSystemTick()
    -- MiniLog("InitGameData after initGameUI cost time =" .. (t4- t3))
    -- MiniLog("InitGameData total cost time =" .. (t4- t1))
end

InitSystem.InitGameUI = function(self, tocFilePath)
    if not GameUI then
        return
    end

    GameUI:setApiId(GetClientInfo():GetAppId())
    if tocFilePath == nil then
        tocFilePath = "ui/mobile/game_start_init.lua"
    end

    local uitoc = tocFilePath
    local tocdir = GetClientInfo():getEnterParam("tocdir")
    if tocdir and string.len(tocdir) > 0 then
        uitoc = tocdir
    end

    local screenw, screenh = GetClientInfo():getClientWindowSize(0, 0)
    GameUI:Create(uitoc, 1280.0, 720.0, screenw, screenh, PlatformUtility:isMobile() and UI_PLATFORM_MOBILE or UI_PLATFORM_PC, GetIWorldConfig():getGameData("lang"))
    GameUI:SetCurrentCursor("normal")
end

_G.InitSystem = SandboxClass.InitSystem:new()
