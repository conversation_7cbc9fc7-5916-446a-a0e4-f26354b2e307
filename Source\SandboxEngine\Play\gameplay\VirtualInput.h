﻿#pragma once

#include "OgrePrerequisites.h"
//tolua_begin
enum UIButtonKey
{
	BUTTON_JUMP,//0
	BUTTON_ACTION,
	BUTTON_SNEAK,
	BUTTON_RUN,
	BUTTON_FLYUP,
	BUTTON_FLYDOWN,
	BUTTON_SCREENTAP,

	BUTTON_FORWARD,
	BUTTON_BACK,
	BUTTON_LEFT,
	BUTTON_RIGHT,
	BUTTON_FORWARDLEFT,
	BUTTON_FORWARDRIGHT,

	BUTTON_GUNRELOAD,
	BUTTON_GUNAIM,

	BUTTON__PASSBALL_OR_CATCHBALL,
	BUTTON_SKILL,//16  模拟PC的鼠标右键，注意是skill
};
//tolua_end

class VirtualInput //tolua_exports
{ //tolua_exports

public:
	//tolua_begin
	virtual bool GetButton(UIButtonKey key) = 0;
	virtual bool GetButtonDown(UIButtonKey key) = 0;
	virtual bool GetButtonUp(UIButtonKey key) = 0;
	virtual bool GetKey(int id) = 0;
	virtual bool GetKeyDown(int id) = 0;
	virtual bool GetKeyUp(int id) = 0;

	virtual void ResetKey(int id) = 0;
	virtual void ResetAllInput() = 0;

	//only get 0,1,-1
	virtual void GetDpadValueRaw(float &forward, float &strafe) = 0;
	//will get true value in RockerMode
	virtual void GetDpadValue(float &vertical, float &horizontal) = 0;

	//tolua_end
protected:
	//For keyboard!  32-space; 15 -shift
	std::map<short, bool> m_XInputKeyMap;
	std::map<short, bool> m_KeyMap;
	std::map<short, bool> m_KeyUpMap; 
	std::map<short, bool> m_KeyDownMap; 

	std::map<short, int> m_KeyDownTime;
	std::map<short, int> m_KeyUpMarks;
	std::map<short, int> m_KeyDownMarks;
}; //tolua_exports