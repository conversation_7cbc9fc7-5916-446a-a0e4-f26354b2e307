#pragma once
#include <string>
#include <functional>
#include "SandboxEngine.h"
#include "defdata.h"
#include "HomeDefdata.h"
#include "DefDataTable.h"
#include "chunkrandom.h"
#include "BaseClass/SharePtr.h"
#include "Graphics/Texture2D.h"
#include "SandboxGame/SandboxGameDef.h"
const int CustomBlockModelMaxId = 4000;
const int CustomBlockModelMinId = 2001;

enum
{
    ITEM_DEFAULT = 101,
    PROJECTILE_DEFAULT = 12002,
    MONSTER_DEFAULT = 3401,
    CRAFTING_DEFAULT = 8,
    FURNACE_DEFAULT = 1,
};

enum
{
    CRCCODE_CRAFTING = 0,
    CRCCODE_ACHIEVE,
    CRCCODE_ITEMS,
    CRCCODE_MOBSPAWNER,
    CRCCODE_MONSTER,
    CRCCODE_HORSEEGG,
    CRCCODE_FURNACE,
    MAX_CRCCODE_TYPE
};

struct SurviveObjectiveDef;
struct SurviveTaskDef;
struct TechTree;
class ICsvLoadConfig;
class SoundDef;
class TriggerResourceType;
class ParticleDef;
class ClientMob;
namespace MINIW
{
    class CSVParser;
}

using CSVParser = MINIW::CSVParser;

class EXPORT_SANDBOXENGINE DefManagerProxy
{
public:
    DefManagerProxy();
    virtual ~DefManagerProxy() = default;
    virtual MonsterDef* getMonsterDef(int id, bool takeplace = false, bool bUseOne = false)=0;
    virtual MonsterDef* addMonsterDef(int id, int type, const std::string& model = "", const std::string& name = "") = 0;
    virtual MonsterDef* addMonsterDefByCopy(int id, int copyId, int type, const std::string& model = "", const std::string& name = "") = 0;
    virtual MonsterDef* getIgnoreEditPlugin(int id, bool takeplace = false)=0;
    virtual DefDataTable<ToolDef>& getToolTable()=0;
    virtual BlockDef* getBlockDef(int id, bool takeplace = true)=0;
    virtual BlockDef* addBlockDef(int id, int type, const std::string& model = "", const std::string& name = "", const std::string& desc = "") = 0;
    virtual BlockDef* addBlockDefByCopy(int id, int type, int copyId) = 0;
    virtual const DieInfoCsvDef* getDieinfoCsvDef(int id) = 0;
    virtual const ToolDef* getToolDef(int id)=0;
    virtual const EquipGroupDef* getEquipGroupDef(int id) = 0;
    virtual ToolDef* addToolDefByCopy(int id, int copyId) = 0;
    virtual ToolDef* getOriginalToolDef(int id) = 0;
    virtual ProjectileDef* addProjectileDefByCopy(int id, int copyId) = 0;
    virtual ProjectileDef* getProjectileDef(int id, bool takeplace = false) = 0;
    virtual ProjectileDef* getOriginalProjectileDef(int id) = 0;
    virtual const FoodDef* getFoodDef(int id) = 0;
    virtual const FoodDef* getFoodDefByIndex(int index) = 0;
    virtual FoodDef* addFoodDefByCopy(int id, int copyId) = 0;
    virtual FoodDef* getOriginalFoodDef(int id) = 0;
    virtual void removeFoodDef(int id) = 0;
    virtual int GetPlayerBuffAtkType(int buff) = 0;
    virtual int getFoodNum() = 0;
    virtual bool isFood(int id) = 0;
    virtual const BiomeGroupDef* getBiomeGroupDef(int id) = 0;
    virtual int getBiomeGroupNum() = 0;
    virtual const BiomeGroupDef* getBiomeGroupDefByIndex(int index) = 0;
    virtual ItemDef* getItemDef(int id, bool takeplace = false) = 0;
    virtual Rainbow::SharePtr<Rainbow::Texture2D> getItemTexByItemId(int itemid)=0;
    virtual int	getItemNum() = 0;
    virtual int getNpcTradeNum() = 0;
    virtual int getNpcPlotDefNum() = 0;
    virtual WorkbenchTechCsvDef* findItembenchTech(int itemid) = 0;
    virtual TechTree* getWorkbenchTechTree(int level) = 0;
    virtual PhysicsActorDef* getPhysicsActorDef(int id) = 0;
    virtual PhysicsActorDef* addPhysicsActorDefByCopy(int id, int copyId) = 0;
    virtual std::vector<int>& getNpcPlotIds() = 0;
    virtual NpcPlotDef* getNpcPlotDef(int id) = 0;
    virtual RoleSkinDef* getRoleSkinDef(int id) = 0;
    virtual RoleSkinDef* GetRoleSkinByIndex(int index) { return nullptr; }
    virtual RoleSkinDef* GetRoleSkinByName(const std::string& name) { return nullptr; }
    virtual int GetRoleSkinCount() { return 0; }
    virtual RoleDef* getRoleDef(char model, int geniuslv) = 0;
    virtual ParticleDef* getParticleDefByPath(const char* path) = 0;
    virtual ParticleDef* getParticleDefByPath2(const char* path) = 0;
    virtual const char* getStringDef(int id) = 0;
    virtual const BuffEffectEnumDef* getBuffEffectEnumDef(int id) = 0;
    virtual BiomeDef* getBiomeDef(int id) = 0;
    virtual BiomeDef* addBiomeDefByCopy(int id, int copyId) = 0;
    virtual int getBiomeDefCount() = 0;
    virtual int getCurBiomeBuff(int biomeid) = 0;
    virtual int getCurBiomeBuffFixed(int biomeid, float fixed, std::vector<int> SleepDebuff) = 0;
    virtual const HorseDef* getHorseDef(int id) = 0;
    virtual StoreHorseDef* getStoreHorseByID(int id) = 0;
    virtual const HorseAbilityDef* getHorseAbilityDef(int id) = 0;
    virtual int getRealStatusId(int id, int lv) = 0;
    virtual const BuffDef* getStatusDef(int id) = 0;
    virtual const BuffDef* getBuffDef(int id, int level) = 0;
    virtual BuffDef* getStatusDefRaw(int id) = 0;			    // ֻ�� m_BuffTable ��ȡ���壬 �µ�prefeb���ϵͳ��
    virtual BuffDef* addStatusDefRawByCopy(int id, int copyId) = 0;	// �� m_BuffTable ���Ӷ���
    virtual bool finalizeStatusDefRaw() = 0;
    virtual void removeStatusDefRaw(int id) = 0;
    virtual const NpcTradeDef* getNpcTradeDef(int id) = 0;
    virtual void getAllNpcTradeIds(std::vector<int>& result) = 0;
    virtual void setCurAccordEnchants(int tooltype) = 0;
    virtual const EnchantMentDef* getEnchantMentDef(int type) = 0;
    virtual int getCurAccordEnchantsNum() = 0;
    virtual const EnchantDef* getCurAccordEnchantDef(int index) = 0;
    virtual const EnchantDef* getEnchantDef(int id) = 0;
    virtual const char* getWildmanNameDef(int id) = 0;
    virtual int getWildmanNameDefNum() = 0;
    virtual TriggerActDef* getTriggerActDef(int id) = 0;
    virtual const PhysicsPartsDef* getPhysicsPartsDef(int id) = 0;
    virtual std::map<int, int>& getMineToolIcon() = 0;
    virtual const GameRuleDef* getGameRuleDef(int id) = 0;
    virtual const RuleOptionDef* getRuleOptionDef(int id) = 0;
    virtual DefDataTable<GameRuleDef>& getGameRuleTable() = 0;
    virtual DefDataTable<RuneDef>& getRuneDefTable() = 0;
    virtual std::map<int, std::set<int>>& GetMaterialIdSet() = 0;
    virtual const RuneDef* getRuneDef(int id) = 0;
    virtual const GunDef* getGunDef(int id) = 0;
    virtual GunDef* getOrignalGunDef(int id) = 0;

    virtual int getGunSlotNum(int id) = 0;
    virtual const GunComponentDef *getGunComponentDef(int id) = 0;

    virtual ItemDef* getAutoUseForeignID(int id) = 0;
    virtual const ExtremityScoreDef* getExtremityScoreDef(int Type, int GoalID = 0) = 0;
    virtual CraftingDef* getCraftingDef(int id, bool takeplace = false, bool getDefause = true) = 0;
    virtual CraftingDef* getCraftingDefByCopy(int id, int nCopyId) = 0;
    virtual void removeCraftingDef(int id) = 0;
    virtual void finalizeCrafting() = 0;
    virtual bool isCraftingMaterial(int id) = 0;
    virtual const ItemSkillDef* getItemSkillDef(int id) = 0;
    virtual const std::vector<int> GetBuffByNature(char nature) = 0;
    virtual ParticleDef* getParticleDef(int id) = 0;
    virtual bool checkItemCrc(int itemid) = 0;
    virtual bool isNpcTrade(int monsterid) = 0;
    virtual const PetInfoDef* getPetDef(int id, int stage, int quality) = 0;
    virtual int getNpcTaskDefNum() = 0;
    virtual std::vector<int>& getNpcTaskIds() = 0;
    virtual NpcTaskDef* getNpcTaskDef(int id) = 0;
    virtual int getNpcShopDefNum() = 0;
    virtual NpcShopDef* getNpcShopDef(int id) = 0;
    virtual int getNpcShopNpcInnerId(std::string key, unsigned int foreign_id, int default_value) = 0;
    virtual std::vector<int> getNpcShopIds() = 0;
    virtual bool isCustomStatus(int id) = 0;
    virtual const PlayActDef* getPlayActDef(int id) = 0;
    virtual const PhysicsMaterialDef* getPhysicsMaterialDef(int id) = 0;
    virtual SoundDef* getSoundDef(int id) = 0;
    virtual SoundDef* getSoundDefByPath(const char* path) = 0;
    virtual bool isEquipStatus(int id) = 0;
    virtual int addEquipStatus(int id) = 0;
    virtual const BuffDef* getToolStatusDef(int id) = 0;
    virtual int getStatusIdByEquipId(int id) = 0;
    virtual int addToolStatus(int id) = 0;
    virtual int getStatusIdByToolId(int id) = 0;
    virtual const BuffEffectDef* getBuffEffectDef(int id) = 0;
    virtual const BuffEffectSliderDef* getBuffEffectSliderDef(int id) = 0;
    virtual const HomeItemDef* getHomeItemDef(int itemid) = 0;
    virtual int getCraftEmptyHandID() = 0;
    virtual bool checkCrcCode(int t) = 0;
    virtual const FurnaceDef* getFurnaceDefByMaterialID(int materialid, bool takeplace = false) = 0;
    virtual const FurnaceDef* getFurnaceDefByMaterialIDWithType(int materialid, bool takeplace, int type) = 0;
    virtual int getMaxID() = 0;
    virtual std::vector<BlockDef*>& getBlockDefTable() = 0;
    virtual DefDataTable<ChestDef>& getChestDefTable() = 0;
    virtual DefDataTable<ChestSpawnDef>& getChestSpawnTable() = 0;
    virtual DefDataTable<SkinningToolDef>& getSkinningToolTable() = 0;
    virtual const PhysicsPartKeyDef* getPhysicsPartKeyDefByIndex(int PartsId, int index) = 0;
    virtual const CraftingDef* findCrafting(int resultId) = 0;
    virtual const CraftingDef* findCrafting(int gridx, int gridy, const int* ids, const int* counts, int& nresult) = 0;
    virtual const CraftingDef* findCraftingByDoubleWeapon(int rightWeaponId, int leftWeaponId) = 0;
    virtual const CraftingDef* findDisassemble(int resultId) = 0;
    virtual DefDataTable<HorseEggDef>& getHorseEggTable() = 0;
    virtual DefDataTable<MobSpawnerDef>& getMobSpawnerTable() = 0;
    virtual void filterStringDirect(char* content) = 0;
    virtual bool findHomeItemDefFunctionId(int itemid, int functionid) = 0;
    virtual int getSoundTypeDefNum(int type) = 0;
    virtual int	getSoundIdByTypeAndIndex(int type, int index) = 0;
    virtual int getParticleDefTypeNum(int type) = 0;
    virtual ParticleDef* getParticleDefByType(int type, int index) = 0;
    virtual const HomeChunkInfoDef* getHomeChunkDef(int chunx, int chunz) = 0;
    virtual BiomePlantTryDef& getBiomePlantTries() = 0;
    virtual PlanetBiomeGeneDef* getPlanetBoimeGeneDef() = 0;
    virtual int getRandomDungeonSpawner(ChunkRandGen* randgen) = 0;
    virtual DefDataTable<MonsterDef>& getMonsters() = 0;
    virtual void addDefByCustomModel(int id, int type, std::string filename = "", std::string name = "", std::string desc = "", Rainbow::Vector3f box = Rainbow::Vector3f(0, 0, 0), short involvedid = 0) = 0;
    virtual ItemDef* addItemDef(int id, int type, std::string model = "", std::string name = "", std::string desc = "", short involvedid = 0) = 0;
    virtual ItemDef* addItemDefByCopy(int id, int type, int copyId) = 0;
    virtual GunDef* addGunDef(int id) = 0;
    virtual GunDef* addGunDefByCopy(int id, int copyId) = 0;
    virtual void resetCrcCode(int type) = 0;
    virtual void removeCustom(int id, int type, bool needresetcrc = false, int involvedid = 0) = 0;
    virtual void clearAllCustomItem() = 0;
    virtual bool reloadPhysicsMaterialCSV() = 0;
    virtual bool reloadPhysicsPartsCSV() = 0;
    virtual bool reloadPhysicsPartsTypeCSV() = 0;
    virtual PackGiftDef* getPackGiftDef(int iPackID) = 0;
    virtual const MusicalDef* getMusicalDef(int id) = 0;
    virtual int getNpcPlotConfigurableDefNum() = 0;
    virtual NpcPlotDef* getNpcPlotConfigurableDefByIndex(int index) = 0;
    virtual PhysicsPartsTypeInfoDef* getPhysicsPartsTypeDef(int iType, int iSubType) = 0;
    virtual int getPhysicsPartsConnectDefNum() = 0;
    virtual PhysicsPartsConnectDef* getPhysicsPartsConnectDef(int controlType, int controlSubType, int workType, int workSubType) = 0;
    virtual PhysicsPartsConnectDef* getPhysicsPartsConnectDefByIndex(int index) = 0;
    virtual MonsterDef* getOriginalMonsterDef(int id) = 0;
    virtual int getCraftingDefNum() = 0;
    virtual CraftingDef* getCraftingDefByIndex(int index) = 0;
    virtual int getCookbookNum() = 0;
    virtual CraftingDef* getCookbookDef(int id, bool takeplace = false, bool getDefause = true) = 0;
    virtual CraftingDef* getCookbookByIndex(int index) = 0;
    virtual CraftingDef* findCookbookByResultId(int resultId) = 0;
    virtual bool isCookbookType(int resultId) = 0;
    //ͨ���䷽��ȡ��Ӧ��������
    virtual CraftingDef* getCookBookByDef(std::unordered_map<int, int>& materialMap) = 0;

    virtual bool getItemsByGroup(int id, std::vector<int>& out) = 0;
    virtual int getHomeItemDefDefNum() = 0;
    virtual const HomeItemDef* getHomeItemDefByIndex(int index) = 0;
    virtual void clearTempBlockDef() = 0;
    virtual const SprayPaintDef* getSprayPaintDef(int id) = 0;
    virtual HotkeyDef* getHotkeyDefByKey(const char* key) = 0;
    virtual const OreDef* getOriginalOreDefById(int blockId, bool defaultOreId = true, bool igoremapid = false) = 0;
    virtual VoxelPalette* getVoxlPalette(int i) = 0;
    virtual std::map<std::string, int>& GetParticlesStrDefCsvStrMap() = 0;
    virtual std::map<int, std::string>& GetParticlesStrDefCsvIdMap() = 0;
    virtual std::map<std::string, int>& GetSoundStrDefCsvStrMap() = 0;
    virtual std::map<int, std::string>& GetSoundStrDefCsvIdMap() = 0;
    virtual DefDataTable<OreDef>& getOriginalOreTable() = 0;
    virtual std::string filterString(char* content, bool checknum = true) = 0;
    virtual TriggerActDef* getTriggerActDefByIndex(int index) = 0;
    virtual void getNpcShopChangeConfigStatus(const char* path, std::map<int, std::vector<int> >& mNpcShopChangeInfo) = 0;
    virtual void setNpcShopChangeConfigStatus(const char* path, const std::map<int, std::vector<int> >& mNpcShopChangeInfo) = 0;
    virtual std::vector<CharacterDef>& getCharDefTable() = 0;
    virtual int getTriggerActBySrcAct(int act) = 0;
    virtual const RecordEffectDef* getRecordEffectDef(int id) = 0;
    virtual const SkinActDef* getSkinActDef(int id, int skinid) = 0;
    /**
       ��ȡResourcePackDefCsv������� = 0 
    */
    virtual const ResourcePackDef* getResourcePackDef(int id) = 0;
    virtual const FishingDef* getFishingDef(int id) = 0;
    virtual bool isFishNeedUp(int id) = 0;
    virtual int getOneFishingResult(int biome, int toolId, bool isTempest = 0) = 0;

    virtual const ColorMixDef* getColorMixDef(int id) = 0;
    virtual int getColorMixDefDefNum() = 0;
    virtual int getColorMixDefMixColorID(int id,int mixid) = 0;
    virtual unsigned int getColorMixDefColor(int id = 0) = 0;
    virtual const SummonDef* getSummonByID(int id) = 0;
    
    virtual bool IsInit() = 0;
    virtual STGameZone* countryGameZone(const char* country) = 0;
    virtual const std::string& getCountryCode(const std::string* code) = 0;
    virtual void translateLetter(std::string title, std::string content, std::string& transTitle, std::string& transContent) = 0;
    virtual void translateBookCover(std::string title, std::string author, std::string& transTitle, std::string& transAuthor) = 0;
    virtual std::string getTransStrByKey(TRANSLATE_TYPE type, std::string key, std::string oldVal = "", std::string oldMultiLanVal = "") = 0;
    virtual void getS2(std::string& s2_, std::string& s2t_) = 0;
    virtual int getItemBakeTo(int materialId) = 0;
    virtual int getDriftBottleOfficalTextNum() = 0;
    virtual const DriftBottleOfficialText* getDriftBottleOfficialTextByIndex(int index) = 0;
    virtual int getLettersOfficalTextNum() = 0;
    virtual const LettersOfficialText* getLettersOfficialTextByIndex(int index) = 0;
    virtual std::vector<LettersOfficialText*> getLettersOfficialDef() = 0;
    virtual bool copyLettersOfficialText(std::vector<LettersOfficialText*> value) = 0;
    virtual int getVoiceZone(const char* country) = 0;
    virtual const BlockEffectDef* getBlockEffectDef(const char* blockTypeName) = 0;
    virtual BIOME_TYPE getbiot(const char *name) = 0;
    virtual ENCHANT_TYPE Name2EnchantTypeWrapper(const char* name) = 0;
    virtual std::string getRandomName(int sex) = 0;

    virtual const SurviveObjectiveDef* getSurviveObjectiveDef(unsigned int id) = 0;
    virtual int getSurviveObjectiveDefCsvNum() = 0;
    virtual const SurviveObjectiveDef* getSurviveObjectiveDefByIndex(int index) = 0;
    virtual int getSurviveObjectiveFrontTaskNum(unsigned int taskID) = 0;
    virtual int getSurviveObjectiveFrontSize() = 0;
    virtual const SurviveTaskDef* getSurviveTaskDef(unsigned int taskID) = 0;
    virtual std::vector<int> getSurviveTaskListByObjectiveID(int objectiveID) const = 0;
    virtual int getSurviveTaskDefCsvNum() = 0;
    virtual int getSurviveTaskFrontSize() = 0;
    virtual void ImportTaskDef(std::unordered_map<int, SurviveTaskDef*>& in) = 0;
	virtual void ExportTaskObjective(std::unordered_map<int, std::vector<int>>& out) = 0;
	virtual void ImportTaskObjective(std::unordered_map<int, std::vector<int>>& in) = 0;
    virtual void Import(std::unordered_map<int, SurviveObjectiveDef*>& in) = 0;
    virtual void onParseTask(CSVParser& parser, std::unordered_map<int, SurviveTaskDef*>& out) = 0;
    virtual int getFrontTaskNum(int taskid) = 0;
    virtual void onParseObjective(CSVParser& parser, std::unordered_map<int, SurviveObjectiveDef*>& out) = 0;
    virtual const SurviveTaskDef* getSurviveTaskDefByIndex(int index) = 0;
    virtual bool loadBiomeCSV() = 0;
    virtual ICsvLoadConfig& getCsvLoadConfig() = 0;
    virtual int getTriggerResourceTypeDefNum() = 0;
    virtual int getSoundDefNum() = 0;
    virtual SoundDef* getSoundDefByIndex(int index) = 0;
    virtual TriggerResourceType* getTriggerResourceTypeDefByIndex(int index) = 0;
    virtual int getParticleDefNum() = 0;
    virtual ParticleDef* getParticleDefByIndex(int index) = 0;
    virtual bool isStoreHorseById(int id) = 0;
    virtual void SetClientActorPetTamedOwnerUin(ClientMob* mob) = 0;
    virtual DevUIResourceDef* GetDevUIResourceDef(int id, bool takeplace = false) = 0;
    virtual DungeonsDef* getDungeonsDefById(int id) = 0;
    virtual MonsterStatueDef* getMonsterStatueDefById(int id) = 0;
    virtual MonsterSpawnDef* getMonsterSpawnDef(int type) = 0;
    virtual std::vector<CraftingDef>getCraftingDefByType(int type) = 0;
    virtual std::vector<CraftingDef>getCraftingDefBySubType(int type,int subtype) = 0;
    virtual UgcModelDef* getUgcModelDef(int id) = 0;
    virtual UgcMaterialDef* getUgcMaterialDef(int id) = 0;

    virtual void ParseWCoord(const std::string& str, WCoord& posData) = 0;
    virtual void ParseItemPosData(const std::string& str, ItemPosDataDef& posData) = 0;

    virtual ItemEquipDef* addEquipDef(int id) = 0;
    virtual ItemEquipDef* getEquipDef(int id) = 0;
    virtual void removeEquipDef(int id) = 0;
    virtual CustomGunDef* addCustomGunDef(int id) = 0;
    virtual CustomGunDef* getCustomGunDef(int id) = 0;
    virtual void removeCustomGunDef(int id) = 0;
    virtual ItemInHandDef* getItemInHandDef(int id) = 0;
    virtual ItemStatusDef* addItemStatusDef(int id) = 0;
    virtual ItemStatusDef* getItemStatusDef(int id) = 0;
    virtual bool IsShowUseBtnForItemRightUse(int id) = 0;
    virtual void removeItemStatusDef(int id) = 0;
    virtual CustomPrefabDescInfo* getCustomPrefabDescInfo(std::string name) = 0;
    virtual CustomPrefabDescInfo* addCustomPrefabDescInfo(std::string name) = 0;
    virtual int setModMainTask(const std::string& taskFilePath, const std::string& objectFilePath, std::unordered_map<int, std::vector<int>>& m_TaskObjectiveTableCache, std::unordered_map<int, SurviveTaskDef*>& m_TaskDefTableCache, std::unordered_map<int, SurviveObjectiveDef*>& m_ObjectiveDefTableCache) = 0;
	virtual int ModParseBiomeGroupDef(const std::string& filePath, std::unordered_map<int, BiomeGroupDef*>& cache) = 0;
	virtual void ResetBiomeGroupDefFromCache(std::unordered_map<int, BiomeGroupDef*>& cache) = 0;
	virtual const PlayerAttribCsvDef* getPlayerAttribCsvDef(PlayerAttributeType type) = 0;
    virtual const ArchitecturalBlueprintCsvDef* getArchitecturalBlueprintCsvDef(int id) = 0;
    virtual const BuildingMaintenanceCsvDef* getBuildingMaintenanceCsvDef(int id) = 0;
    virtual std::vector<const BuildingMaintenanceCsvDef*> getAllBuildingMaintenanceCsvDef() = 0;
    virtual BuildReplaceDef* getBuildReplaceDef(int id) = 0;
	virtual OperateUIData* getOperateUIData(int id) = 0;
    virtual void ParseTypeCollides(const std::string& jsonColliedes, std::vector<TypeCollideAABB>& collides) = 0;
    virtual std::map<int, int> getConsumedItemsByProduceItemID(int produceItemID) = 0;
    virtual int getBpTypeIdByItemId(int itemId) = 0;
    virtual int getBpLevelByItemId(int itemId) = 0;
    virtual AiNpcPlayerDef* getAiNpcPlayerDef(int id, bool takeplace = false, bool bUseOne = false) = 0;
    virtual DefDataTable<AiNpcPlayerDef>& getAiNpcPlayerDefs() = 0;
};

EXPORT_SANDBOXENGINE DefManagerProxy* GetDefManagerProxy();


