IsCustomGameEnd = true;
EnterSurviveGameInfo = 
{
	ReopenRoomInvitePlayers = nil;
}

-- 用于将明文云变量转成数据库中的数据格式
-- 以uin为文件名，放在WritePath/data/winserver/目录下
function CloudVarDataRecover(playerid)
	-- 加载明文数据
    local data = ScriptSupportSetting:readFile(tostring(playerid).. ".var", true)
    local oldvalue = loadstring("return " .. data)()
	
	-- 数据pack
	local libvarMgr = SSMgrResource and SSMgrResource:GetVarLibIns()
    local content = libvarMgr:PackContent(oldvalue, playerid) or ""

	-- 数据增加flatbuff头
	local targetfile = tostring(playerid) .. ".pvar"
	zmqMgr_:SaveLibVarStrToFile(targetfile, content)
end

function StandaloneServer_InitOk()
	gFunc_SLOG("StandaloneServer_InitOk, client can connect now!");
	if zmqMgr_ and zmqMgr_.SetInitOk then
        zmqMgr_:SetInitOk();
    end

	if rentLuaEvent then
		rentLuaEvent("room_init_ok")
	end

	if (not WorldMgr.isStudioGameMode or not WorldMgr:isStudioGameMode()) and GetInst("UGCCommon") then
		GetInst("UGCCommon"):EnterCloudServerWorld()
	end
end

function StandaloneServer_Enter()
	gFunc_SLOG("StandaloneServer_Enter 123");
	--通知客机，主机加载好了 客机可以进来了  再来一局的邀请
    if CurWorld and CurWorld.isGameMakerRunMode and
        CurWorld:isGameMakerRunMode() and EnterSurviveGameInfo.NeedInvite and EnterSurviveGameInfo.ReopenRoomInvitePlayers then	
		Log( "call StandaloneServer_Enter 456" );
		for i=1, #(EnterSurviveGameInfo.ReopenRoomInvitePlayers) do
			local uin = EnterSurviveGameInfo.ReopenRoomInvitePlayers[i];
			--AccountManager:route('InviteJoinRoom', uin, {RoomState='load_end',Msg=GetS(4888), PassWorld='1238'});
			Log( "call StandaloneServer_Enter 789" );
			ClientCurGame:InviteJoinRoom(uin, 'load_end', '1238');
		end
		EnterSurviveGameInfo.ReopenRoomInvitePlayers = nil;
		EnterSurviveGameInfo.NeedInvite = false;
	end
	
	if (not WorldMgr.isStudioGameMode or not WorldMgr:isStudioGameMode()) and GetInst("UGCCommon") then
		GetInst("UGCCommon"):EnterCloudServerWorld()
	end
	--CloudVarDataRecover(**********)
end

function BattleFrameReopen()
	--邀请房员再来一局
	if EnterSurviveGameInfo.ReopenRoomInvitePlayers ~= nil then
		return;
	end
	EnterSurviveGameInfo.NeedInvite = false;
	EnterSurviveGameInfo.ReopenRoomInvitePlayers = {};

	local num = ClientCurGame:getNumPlayerBriefInfo();
	for i=1, num do
		local briefInfo = ClientCurGame:getPlayerBriefInfo(i-1);
		if briefInfo ~= nil then
			table.insert(EnterSurviveGameInfo.ReopenRoomInvitePlayers, briefInfo.uin);
		end
	end

	print("kekeke ReopenRoomInvitePlayers", EnterSurviveGameInfo.ReopenRoomInvitePlayers);
	for i=1, #(EnterSurviveGameInfo.ReopenRoomInvitePlayers) do
		local uin = EnterSurviveGameInfo.ReopenRoomInvitePlayers[i];
		--AccountManager:route('InviteJoinRoom', uin, {RoomState='load_begin',Msg=GetS(4888), PassWorld='1238'});
		ClientCurGame:InviteJoinRoom(uin, 'load_begin', '1238');
	end
	EnterSurviveGameInfo.NeedInvite = true;

	--重新加载地图
	if GetClientInfo():GetClientVersion() < 23*256 then
		GetClientGameManagerPtr():gotoGame("StandaloneServer", true);
	else
		GetClientGameManagerPtr():gotoGame("StandaloneServer", MULTI_RELOAD);
	end
	IsCustomGameEnd = false;
end

function UpdateBattleEndInfo(gametime)
	if gametime == 0 then
		if GetInst("BattleReportDataSys") then
			GetInst("BattleReportDataSys"):SysReportData()
		end
		IsCustomGameEnd = true;	
	end
end

function UpdateBattleInfo(stage, gametime)
	if stage == 4 or IsCustomGameEnd then	--游戏结束
		if gametime == 0 then
			BattleFrameReopen();
		end			
	end
end

function BattleFrame_OnEvent(arg1, stage_, gametime_)
	if arg1 == "GE_CUSTOMGAME_STAGE" then
		if stage_ and gametime_ then
            if stage_ == 4 then
				threadpool:work(function()
					-- 等待至少两个tick 等game.end事件触发，等待开发者脚本处理完成  by:liusijia
					threadpool:wait(0.1)
					threadpool:wait(0.1)
					UpdateBattleEndInfo(gametime_)
			   end )
            end
            return
        end
		
		--[[if stage == 4 then
			if gametime == 0 then
				UpdateBattleInfo(stage, gametime);
			end
		else
			UpdateBattleInfo(stage, gametime);
		end]]--
	end
end




