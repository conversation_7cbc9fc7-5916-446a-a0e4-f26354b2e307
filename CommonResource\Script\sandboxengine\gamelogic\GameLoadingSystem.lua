if not SandboxClass then assert(false) return end
--[[
    ClientGame切换加载的lua封装
]]
local GameLoadingSystem = SandboxClass:Class("GameLoadingSystem")

GameLoadingSystem.constructor = function(self)
 
end

GameLoadingSystem.destructor = function(self)

end

_G.GameLoadingSystem = SandboxClass.GameLoadingSystem:new()


UpdateSystemLoading = function(dt)
    if _G.GameLoadingSystem then
        _G.GameLoadingSystem:UpdateLoading()
    end
    
    -- UpdateSystemLoading 调整成只有要加载game的时候才走进来，减少C++调lua的开销 code_by:huangfubin 2022.11.4
    --_G.g_DelayedTaskMgr:Update(dt)
end

GameLoadingSystem.SetMapSceneLoadTips = function(self, tips)
    if ClientGameMgr.setMapSceneLoadTips then
        ClientGameMgr:setMapSceneLoadTips(tips)
    end
end

GameLoadingSystem.SetMapSceneLoadProgress = function(self, progress, max)
    if ClientGameMgr.setMapSceneLoadProgress then
        ClientGameMgr:setMapSceneLoadProgress(progress, max)
    end
end

-- 服务器需要提前初始化ModMgr 避免mod加载失败
ServerModMgrInit = function()
    if _G.GameLoadingSystem then
        _G.GameLoadingSystem:UgcInit()
    end
end

-- loading 处理
GameLoadingSystem.UpdateLoading = function(self)
    if not ClientGameMgr then return end
    local m_LoadingGame = ClientGameMgr:getLoadingGame()
    local m_CurGame = ClientGameMgr:getCurGame()
    if not PlatformUtility:isPureServer() then
        if m_LoadingGame == nil then return end
        local loadstatus = self:updateLoadForGame(m_LoadingGame) --m_LoadingGame:updateLoad()

        if loadstatus == 0 then
            return
        elseif loadstatus < 0 then
            GetClientInfo():setRenderCamera(nil)
            GetClientInfo():setRenderContent(nil)
            m_LoadingGame:unload()
            ClientGameMgr:removeGame(m_LoadingGame)
            ClientGameMgr:setLoadingGame(nil)
            m_LoadingGame = nil
            if GameEventQue then
                GameEventQue:postLoadProgress(1000, -1)
            end
            if SetExitReason then
                pcall(SetExitReason, -loadstatus, true)
            end
            if m_CurGame then
                m_CurGame:reloadScene()
            end
            if UgcAssetFileMgr then
                UgcAssetFileMgr:SetRemoteRoot("")
            end
            return
        end

        if m_CurGame then
            gFunc_setUserTypePointer("ClientCurGame", m_CurGame:getTypeName(), nil)
            if UploadClientInfoToCloudServer then
                UploadClientInfoToCloudServer(1)                
            end
            ClientGameMgr:clearCurGame()
        end
        m_CurGame = m_LoadingGame
        ClientGameMgr:setCurGame(m_LoadingGame)

        if m_CurGame:isInGame() then
            GetClientInfo():setSandboxEngineState(GAME_IN_GAME)
        else
            GetClientInfo():setSandboxEngineState(GAME_WAITING)
        end
    else
        if m_LoadingGame == nil then
            return
        else
            if m_CurGame then
                gFunc_setUserTypePointer("ClientCurGame", m_CurGame:getTypeName(), nil)
                if UploadClientInfoToCloudServer then                
                    UploadClientInfoToCloudServer(1)
                end
                ClientGameMgr:clearCurGame()
            end
        end

        -- 云服部分配置需要拉取之后再初始化  此逻辑需要放在modmgrinit之后，否则modmgr可能会延迟初始化导致加载失败
        if not IsServerConfigOk() then
            print("server config it not ok")
            return
        end

        m_CurGame = m_LoadingGame
        ClientGameMgr:setCurGame(m_LoadingGame)
        m_CurGame:updateLoad()
        m_CurGame:updateLoad()
        InitBreedingItem()
        InitMobGrowTime()
        InitMobLoveTime()
    end

	m_LoadingGame = nil
    ClientGameMgr:setLoadingGame(nil)

	ClientGameMgr:RegisterInputHandler()

    local m_ReloadGameType = ClientGameMgr:getReloadGameType()
	if m_CurGame then
		gFunc_setUserTypePointer("ClientCurGame", m_CurGame:getTypeName(), m_CurGame)
		m_CurGame:applayGameSetData(true)
        Log("updateLoadingGame applayGameSetData")
        
        if ResetHomelandMapParam then
            ResetHomelandMapParam()
        end
        local funcname = m_CurGame:getName() .. "_Enter"
		m_CurGame:beginGame()
		if GetClientInfo():isEditorMode() then
			ClientGameMgr:mapSceneLoadFinish()
        end
		Log("updateLoadingGame beginGame end")
		m_CurGame:setHasBegan(true)

		ClientGameMgr:CheckWindowLostFocus()

        if GetInst("MiniUIManager") then
            GetInst("MiniUIManager"):ClearHistory();
        end

        if m_ReloadGameType == NO_RELOAD or m_CurGame:getTypeName() ~= "MainMenuStage" then
            local funcname = m_CurGame:getName() .. "_Enter"
            gFunc_callLuaFunction(funcname, "")
        end

		if m_ReloadGameType ~= NO_RELOAD and m_CurGame:getName() == "MainMenuStage" then
			if m_ReloadGameType == MULTI_RELOAD then
				if AccountManager:getMultiPlayer() == GAME_NET_MP_GAME_CLIENT then
					AccountManager:setCurWorldId(0)
                    --现在没有setSpecialType的接口，如果需要的话，要在WorldManager里面加接口
					-- AccountManager:setSpecialType(NORMAL_WORLD)
                end
				ClientGameMgr:gotoGame("MPSurviveGame", m_ReloadGameType)
			elseif m_ReloadGameType == SINGLE_RELOAD then
                local desc = AccountManager:getCurWorldDesc()
                if desc and desc.editorSceneSwitch and desc.editorSceneSwitch == 1 then
                    UGCRuntime:GetInst():checkAndGotoGame("SurviveGame", m_ReloadGameType)
                else
				ClientGameMgr:gotoGame("SurviveGame", m_ReloadGameType)
                end
				-- ClientGameMgr:checkAndGotoGame("SurviveGame", m_ReloadGameType)
            end
		end

        --可能存在加载地图前，ClientCurGame为空时调用显示主界面导致地图和主界面都显示的情况,这里在进入游戏地图后再隐藏一次 by：Jeff      
        if HideMiniLobby and m_CurGame:isInGame() then  --在地图内不能打开首页           
            HideMiniLobby()       
        end
    end
end

-- 不同的Game处理
-- 配置下载标识，只有一次
local loadingTime
GameLoadingSystem.Cfg_Loading_Flag = false
GameLoadingSystem.updateLoadForGame = function(self, game)
    if not game then
        return -1
    end
    if game:getTypeName() == "MainMenuStage" then
        game = tolua.cast(game,"MainMenuStage")
        local m_LoadCounter = game:getLoadStepCounter()
        if m_LoadCounter then
            -- 进加载的进度同步minicode
            if isEducationalVersion and MCodeManager then
                MCodeManager:processLoading({type = "initLite", process = m_LoadCounter:getProgress()});
            end
            if m_LoadCounter:getProgress() == 100 then
                MiniLogWarning('LoadStage_Done', CommonUtil:getSystemTick() - loadingTime)
                if not self.Cfg_Loading_Flag then
                    self.Cfg_Loading_Flag = true
                    -- 配置检查下载
                    LoginManager:StartCfgDownload()
                end
                return 1
            else
                self:updateLoadForMainMenuStageNew(m_LoadCounter)  -- 使用新版本加载
                if m_LoadCounter:getProgress() >= 100 then
                    GameEventQue:postLoadProgress(1000, -2)
                    StatisticsTools:StatisticsGameLoadAction(SAID_GAMELOAD4)
                else
                    GameEventQue:postLoadProgress(1000, m_LoadCounter:getProgress())
                end

                return 0
            end
        end
    end

    return game:updateLoad()
end

GameLoadingSystem.preloadForMapScene = function (self)
    -- 要差异化加载的lua列表初始化
    g_LuaPreLoadMgr:InitLuaPathList()
    -- 要差异加载的xml列表初始化
    GetInst('UIManager'):InitXmlPathList()
    -- 要预先加载的lua
    if g_LoadDiffResMgr then
        g_LoadDiffResMgr:PreLoadFirstLuas()
    end
    
    local function preloadCsv()
        -- 材质相关？非新号走这里
        if not self.needPreLoadBlockMaterial then
            local stepMax = BlockMaterialMgr:getInitStepCount()
            local count = 0
            while count < stepMax do
                threadpool:wait(0.01)
                local curStep = BlockMaterialMgr:getInitStep()
                if curStep == -1 then
                    count = count+1
                    BlockMaterialMgr:initStep(count) --RenderSystem 渲染相关要在主线程
                elseif curStep == 1 then
                    count = count+1
                    BlockMaterialMgr:initStep(count)
                elseif curStep >= 3 then
                    count = count + 1
                    BlockMaterialMgr:initStep(count)
                end
            end
        end

        threadpool:wait(1) --等待一秒，让script热更或者登录显示逻辑跑起来
        
        -- csv配置表 --C++那边只分了1-10段，11就把全部的加载完，有修改的话这里也要改下
        for i=1, 11 do
            DefMgr:loadInLoading(i)
            threadpool:wait(0.01)
        end
        GetClientApp():SomeInitAfterCsvLoad()
        -- 插件库相关？
        threadpool:wait(0.01)
        self:ModMgrInit()

        InitBreedingItem()
        InitMobGrowTime()
	InitMobLoveTime()
    end

    local function preloadResDiff()
        local count = #g_LuaPreLoadMgr.LoadInLoadingXmls
        for i = 1, count do
            local uipath = g_LuaPreLoadMgr.LoadInLoadingXmls[i]
            if uipath then
                if g_LoadDiffResMgr then
                    local replacePath = g_LoadDiffResMgr:GetReplaceXml(uipath)
                    GameUI:ParseUIInXml(replacePath)
                else
                    GameUI:ParseUIInXml(uipath)
                end
            end
        end
    end

    local function preLoadFiles()
        for i=1, #g_LuaPreLoadMgr.LoadInLoadingLuas do
            local luapath = g_LuaPreLoadMgr.LoadInLoadingLuas[i]
            if luapath then
                g_LuaPreLoadMgr:LoadALuaFile(luapath)
            end
        end
        -- 分帧预加载其他部分lua
        for i=1, #g_LuaPreLoadMgr.NeedToLoadLuas do
            local luapath = g_LuaPreLoadMgr.NeedToLoadLuas[i]
            if luapath then
                g_LuaPreLoadMgr:LoadALuaFile(luapath)
            end
        end

        preloadCsv()
        preloadResDiff()
    end

    threadpool:work(preLoadFiles)
   
    if TriggerScriptMgr:GetSingletonPtr() then
        TriggerScriptMgr:GetSingletonPtr():InitSupportScripts();
    end
end

-- by liusijia 调整加载函数
local stage_1_allloadfinish = nil
local stage_2_allloadfinish = nil
local stage_1_step_count = 58
local stage_2_step_count = 40
GameLoadingSystem.SetStageStepCount = function (self, count)
    self.stage_step_count = count or stage_1_step_count
end

GameLoadingSystem.SetStep = function (self, loadcounter)
    loadcounter:step()
    self.stage_step_count = self.stage_step_count - 1
    return self.stage_step_count
end

GameLoadingSystem.updateLoadForMainMenuStageNew = function (self, loadcounter)
    if loadcounter:getStage() == 0 then
        local stagecounter = loadcounter:step()
        if stagecounter == 1 then
            loadingTime = CommonUtil:getSystemTick()
        end
        -- 前面空几帧防止anr
        if loadcounter:stageCompleted() then
            loadcounter:gotoStage(1, stage_1_step_count, stage_1_step_count)
        end
    elseif loadcounter:getStage() == 1 then
        if stage_1_allloadfinish == nil then
            self:SetStageStepCount(stage_1_step_count)
            MiniLogWarning("Stage 1-1")
            stage_1_allloadfinish = false
            threadpool:work(function ()
                local startTime = CommonUtil:getSystemTick()
                self:SetStep(loadcounter)
                pcall(gFunc_LogTimeStamp,"Preload first lua Start")
                -- 要差异化加载的lua列表初始化
                g_LuaPreLoadMgr:InitLuaPathList()

                -- 要差异加载的xml列表初始化
                GetInst('UIManager'):InitXmlPathList()

                -- 要预先加载的lua
                if g_LoadDiffResMgr then
                    g_LoadDiffResMgr:PreLoadFirstLuas()
                end

                threadpool:wait(0)
                local extraCount = 10
                local lastCount = self:SetStep(loadcounter) - extraCount

                -- 加载lua
                local t1 = CommonUtil:getSystemTick()
                local t2 = t1
                local loadinloading = g_LuaPreLoadMgr.LoadInLoadingLuas
                local loadingLen = #loadinloading
                local loadcount = 0
                local stepcount = 0
                local loadingStepIn = math.ceil(loadingLen / lastCount)
                local curStepIn = 0
                for i,luapath in ipairs(loadinloading) do
                    g_LuaPreLoadMgr:LoadALuaFile(luapath)
                    loadcount = loadcount + 1
                    curStepIn = curStepIn + 1
                    -- 每n个文件检测一次 中断更新进度条
                    if loadcount >= 5 then
                        loadcount = 0
                        t2 = CommonUtil:getSystemTick()
                        if t2 - t1 >= 100 then
                            t1 = t2
                            stepcount = stepcount + 1
                            threadpool:wait(0)
                        end
                    end

                    if curStepIn >= loadingStepIn then
                        curStepIn = 0
                        self:SetStep(loadcounter)
                    end
                end

                print("loading stage1 LoadInLoadingLuas len", loadingLen, lastCount, loadingStepIn, curStepIn, stepcount)
                print("loading stage1 LoadInLoadingLuas time", CommonUtil:getSystemTick() - startTime)
                startTime = CommonUtil:getSystemTick()

                threadpool:wait(0)
                self:SetStep(loadcounter)

                --是否开启老UI按需加载
                if GameUI:IsUseAutoLoadLegacyUI() ==false then
                    for i, uipath in ipairs(g_LuaPreLoadMgr.LoadInLoadingXmls) do
                        GameUI:ParseUIInXml(uipath)               
                    end
                end
                print("loading stage1 LoadInLoadingXmls", CommonUtil:getSystemTick() - startTime)
                startTime = CommonUtil:getSystemTick()
                
                threadpool:wait(0)
                self:SetStep(loadcounter)

                -- 材质初始化
                pcall(gFunc_LogTimeStamp,"BlockMaterialMgr init Start")
                BlockMaterialMgr:initStep(1)
                BlockMaterialMgr:initStep(2)
                threadpool:wait(0)
                BlockMaterialMgr:initStep(4)
                pcall(gFunc_LogTimeStamp,"BlockMaterialMgr init end")

                threadpool:wait(0)
                self:SetStep(loadcounter)
                DefMgr:loadInLoading(1)
                DefMgr:loadInLoading(2)
                DefMgr:loadInLoading(3)
                DefMgr:loadInLoading(4)
                threadpool:wait(0)
                self:SetStep(loadcounter)

                DefMgr:loadInLoading(5)
                DefMgr:loadInLoading(6)
                DefMgr:loadInLoading(7)
                DefMgr:loadInLoading(8)
                threadpool:wait(0)
                self:SetStep(loadcounter)

                DefMgr:loadInLoading(9)
                DefMgr:loadInLoading(10)
                DefMgr:loadInLoading(11)
                threadpool:wait(0)
                self:SetStep(loadcounter)

                GetClientApp():SomeInitAfterCsvLoad()
                threadpool:wait(0)

                self:UgcInit()
                copyAvatarPkgFile() 
                threadpool:wait(0)
                self:SetStep(loadcounter)
                print("loading stage1 copyAvatarPkgFile", CommonUtil:getSystemTick() - startTime)
                startTime = CommonUtil:getSystemTick()

                InitBreedingItem()
                InitMobGrowTime()
                InitMobLoveTime()
                print("loading stage1 InitBreedingItem", CommonUtil:getSystemTick() - startTime)
                startTime = CommonUtil:getSystemTick()

                stage_1_allloadfinish = true
                self:SetStep(loadcounter)
                --MiniBase初始化成功通知                            
                SandboxLua.eventDispatcher:Emit(nil, "MiniBase_EngineInit",  MNSandbox.SandboxContext():SetData_Number("code", 0))
                MiniLogWarning("Stage 1-2")
            end)
        end

        if stage_1_allloadfinish then
            local lastProg = stage_2_step_count
            if not loadcounter:stageCompleted() then
                lastProg = 100 - loadcounter:getProgress()
            end
            loadcounter:gotoStage(2, lastProg, lastProg)
            MiniLogWarning("Stage 1-3:" .. tostring(lastProg))
        end
    elseif loadcounter:getStage() == 2 then
        if not stage_2_allloadfinish then
            stage_2_allloadfinish = true
            self:SetStageStepCount(stage_2_step_count)
            MiniLogWarning("Stage 2-1")
            threadpool:work(function ()
                threadpool:wait(0)
                -- 插件库相关初始化？
                pcall(gFunc_LogTimeStamp,"ModMgrInit Start")
                self:ModMgrInit() --代码关联比较早，需要放在前面初始化
                self:SetStep(loadcounter)
                -- 加载跟登录的转圈同时处理，在LoginManager中等待此处加载完成再进入大厅 一般都很快
                pcall(gFunc_LogTimeStamp,"Preload second lua Start")
                local t1 = CommonUtil:getSystemTick()
                local startTime = t1
                local t2 = t1
                local loadcount = 0
                local stepcount = 0
                local extraCount = 1
                local loadinloading = g_LuaPreLoadMgr.NeedToLoadLuas
                local loadingLen = #loadinloading
                local lastCount = self:SetStep(loadcounter) - extraCount
                local loadingStepIn = math.ceil(loadingLen / lastCount)
                local curStepIn = 0
                print("loading stage2 NeedToLoadLuas len", loadingLen, lastCount, loadingStepIn)
                for i, luapath in ipairs(loadinloading) do
                    g_LuaPreLoadMgr:LoadALuaFile(luapath)
                    loadcount = loadcount + 1
                    curStepIn = curStepIn + 1
                    -- 每n个文件检测一次 中断更新进度条
                    if loadcount >= 10 then
                        loadcount = 0
                        t2 = CommonUtil:getSystemTick()
                        if t2 - t1 >= 100 then
                            t1 = t2
                            stepcount = stepcount + 1
                            threadpool:wait(0)
                        end
                    end

                    if curStepIn >= loadingStepIn then
                        curStepIn = 0
                        self:SetStep(loadcounter)
                    end
                end

                -- WWW_file_version2()
                -- 全部加载，需要回滚的话，放开这里
                --StepLoad_LoadAll()

                threadpool:wait(0)
                print("loading stage2 time", CommonUtil:getSystemTick() - startTime)
                startTime = CommonUtil:getSystemTick()
                pcall(gFunc_LogTimeStamp,"Preload second lua End stepcount:" .. stepcount)
            
                threadpool:wait(0)
                -- 插件库相关初始化？
                --pcall(gFunc_LogTimeStamp,"ModMgrInit Start")
                --self:ModMgrInit()
                self.loadLuafinish = true --到这里lua预加载就结束了
                local totalCount = #g_LuaPreLoadMgr.NeedToLoadLuas + #g_LuaPreLoadMgr.LoadInLoadingLuas
                pcall(gFunc_LogTimeStamp,"ModMgrInit End, load luacount " .. totalCount)
                MiniLogWarning("Stage 2-2")
            end)
        -- elseif loadcounter:stageCompleted() then
        end

        -- 留一格
        if self.loadLuafinish then
            local lastProg = 3
            if not loadcounter:stageCompleted() then
                lastProg = 100 - loadcounter:getProgress()
            end

            MiniLogWarning("Stage 2-3:" .. tostring(lastProg))
            loadcounter:gotoStage(3, lastProg, lastProg)
            loadcounter:setStep(lastProg - 1)
       end
    elseif loadcounter:getStage() == 3 then
        loadcounter:step()
        MiniLogWarning("Stage 3-1" .. loadcounter:getProgress())
    end
end

-- 插件库相关？
GameLoadingSystem.ModMgrInit = function (self)
    if self.isModMgrInit then
        return
    end
    self.isModMgrInit = true
    print("GameLoadingSystem.ModMgrInit")
    _G.ModEditorMgr = SandboxClass.ModEditManager:new(ModEditorMgr) 
    ModEditorMgr:load()
    ModFileMgr:checkInitialMod("data/mods")
    ModFileMgr:refreshAllModsPath("data/mods")
    _G.ModMgr = SandboxClass.ModManager:new(ModMgr) 
    --Android上可能是生效时序的问题，直接执行init，调用 ParseMapModPkgs 认的还是C++的 ModMgr:parseMod code_by:huangfubin 2022.1.22
    threadpool:delay(0.5,function ()
        ModMgr:init()
    end)
end
-- ugc加载
GameLoadingSystem.UgcInit = function (self)
    if self.isUgcInit then return end
    self.isUgcInit = true
    if not PlatformUtility:isPureServer() then    
        GetInst("UGCCommon"):UgcInit()
    end
    self:ModMgrInit()
end

GameLoadingSystem.IsModMgrInited = function(self)
    return self.isModMgrInit
end
