﻿#ifndef _AI_NPC_H_
#define _AI_NPC_H_
#include "ClientActorLiving.h"
#include "ClientPlayer.h"
#include "AINpcPlayerTask.h"
#include "defdata.h"

class ChunkIOMgr;

class EXPORT_SANDBOXGAME AINpcPlayer: public ClientPlayer
{
	DECLARE_SCENEOBJECTCLASS(AINpcPlayer)
typedef ClientPlayer _Super;
public:

	virtual bool init(int uin, const char* nickname, int playerindex, const char* customjson) override;
	virtual int getObjType() const override { return  OBJ_TYPE_AIPLAYER; }
	 
    virtual bool saveToFile(long long owid=0, ChunkIOMgr *iomgr=NULL) override {return true;}
	virtual void tick() override;
	
    bool setCurrentShortCutItem(int item_id);
    void setCurrentShootRequestID(const std::string & request_id) {m_ShootRequestID = request_id;}
    const std::string & getCurrentShootRequestID() {return m_ShootRequestID;}
   
	
	void setAINPCMoveTarget(const WCoord &target);

  ///////////////////////////////////////////////////////////////
	bool isInteracting() { return false; }
    void SetMotionType(AI_MOTION_TYPE motp) { m_MotionType = motp; };
    AI_MOTION_TYPE GetMotionType() { return m_MotionType; };
    const MonsterDef *getDef()
	{
		return m_Def;
	}

	// Home和Trace相关方法 (移植自ClientMob)
	void setHome(int iHomeDist, int x, int y, int z);
	void setHomeDist(int iHomeDist)
	{
		m_HomeDist = iHomeDist;
	}
	bool isInHomeDist(int x, int y, int z);
	int getHomeDist() { return m_HomeDist; }
	
	// 个人AINPC相关
	void setPersonalAINPCFlag(bool flag) { m_isPersonalAINPC = flag; }
	bool isPersonalAINPC() const { return m_isPersonalAINPC; }
	void incrementNoPlayerNearbyTimer() { m_noPlayerNearbyTimer++; }
	void resetNoPlayerNearbyTimer() { m_noPlayerNearbyTimer = 0; }
	int getNoPlayerNearbyTimer() const { return m_noPlayerNearbyTimer; }
	
	// AINPC背包生成系统
	void generateRandomBackpack();
	void generateBackpackByType(int chestType);
	int selectRandomChestType();
	
	// AINPC装备生成系统
	void generateRandomEquipment();
	void generateEquipmentByType(int chestType);
	int selectRandomEquipmentChestType();
	EQUIP_SLOT_TYPE getEquipSlotTypeByItemId(int itemId);
	
	// AINPC PlayerBriefInfo初始化
	static void initializePlayerBriefInfo(PlayerBriefInfo* info, int uin, const std::string& name, const WCoord& spawnPos);

	void setTraceDist(int iTraceDist)
	{
		m_TraceDist = iTraceDist;
	}
	int getTraceDist()
	{
		return m_TraceDist;
	}

	// 回家相关方法 
	bool isNeedRetHome() { return m_isNeedRetHome; }
	void setNeedRetHome(bool b) { m_isNeedRetHome = b; }

	// 逃跑相关方法 
	void setNeedRunAway(bool state) { m_bNeedRunAway = state; }
	bool isNeedRunAway() { return m_bNeedRunAway; }

	// 恐慌相关方法 
	void setPanic(bool panic) { m_Panic = panic; }
	bool isPanic() { return m_Panic; }

	// SpawnPoint相关方法 
	void setSpawnPoint(const WCoord& blockpos);
	WCoord& getSpawnPoint() { return m_SpawnPoint; }


	template<typename T, typename ...Args>
	void addAiTask(int iPriority, Args... args)
	{
		if (nullptr == m_AITask)
		{
			m_AITask = ENG_NEW(AINpcPlayerTask)(this);
		}
		T* NewAI = ENG_NEW(T)(this, args...);
		m_AITask->addTask(iPriority, NewAI);

		/*if (IsUseAILua())
		{
			NewAI->m_bPriority = iPriority;
			AIBase* NewAIBase = NewAI;
			MNSandbox::GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCurTaskAdd", "u[AIBase]", NewAIBase);
		}*/
	}

	template<typename T, typename ...Args>
	void addAiTaskTarget(int iPriority, Args... args)
	{
		if (nullptr == m_AITaskTarget)
		{
			m_AITaskTarget = ENG_NEW(AINpcPlayerTask)(this);
		}
		T* NewAI = ENG_NEW(T)(this, args...);
		m_AITaskTarget->addTask(iPriority, NewAI);

		/*if (IsUseAILua())
		{
			NewAI->m_bPriority = iPriority;
			AIBase* NewAIBase = NewAI;
			MNSandbox::GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCurTaskTargetAdd", "u[AIBase]", NewAIBase);
		}*/
	}
private:
    int m_LastBreathTime;
    std::string m_ShootRequestID;
    void keepBreath();
	void initAIComponent();
	bool IsUseAILua() { return false; }

    AINpcPlayerTask* m_AITask;
    AINpcPlayerTask* m_AITaskTarget;

    AI_MOTION_TYPE m_MotionType;

    MonsterDef * m_Def;

    // Home和Trace相关成员变量 (移植自ClientMob)
    int m_TraceDist;
    int m_HomeDist;
	bool m_isNeedRetHome;
	
	// 个人AINPC相关成员变量
	bool m_isPersonalAINPC;         // 是否为个人刷新的AINPC
	int m_noPlayerNearbyTimer;      // 周围没有玩家的计时器
	
	// AINPC背包生成相关静态数据
	static const std::vector<int> AINPC_CHEST_TYPES;
	
	// AINPC装备生成相关静态数据 (使用不同的chestid来配置不同的装备套装)
	static const std::vector<int> AINPC_EQUIPMENT_CHEST_TYPES;

	// SpawnPoint相关成员变量 (移植自ClientMob)
	WCoord m_SpawnPoint;  //出生点

	bool m_Panic;
	bool m_bNeedRunAway;


};

#endif // _AI_NPC_H_