
#include "AIPlayerAtk.h"
#include "ActorVision.h"
#include "ActorVehicleAssemble.h"
#include "RiddenComponent.h"
#include "ToAttackTargetComponent.h"
#include "ActorBodySequence.h"
#include "navigationpath.h"
#include "PathEntity.h"
#include "ClientPlayer.h"
#include "LuaInterfaceProxy.h"
#include "AINpc.h"

AIPlayerAtk::AIPlayerAtk(AINpcPlayer *pActor, bool trace, float speed) :AIBase(pActor), m_trace(trace),m_TraceTimer(0),m_Speed(speed),m_AttackTick(0), m_PathEntity(NULL)
{
	setMutexBits(3);
	m_AttackDistance = 200;
}

AIPlayerAtk::~AIPlayerAtk()
{
	if(m_PathEntity) m_PathEntity->Release();
}

bool AIPlayerAtk::willRun()
{
	ClientActor *target = nullptr;
	auto targetComponent = m_pAINpcPlayer->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target || target->isDead() || target->needClear())
	{
		return false;
	}

	if(atkDist(target)) return true;

	//m_pAINpcPlayer->faceWorldPos(target->getLocoMotion()->getPosition()/100, 180.0f, 180.0f);
	
	if(m_PathEntity) m_PathEntity->Release();
	m_PathEntity = m_pAINpcPlayer->getNavigator()->getPathTo(target);
	return m_PathEntity != NULL;
}

bool AIPlayerAtk::continueRun()
{
	ClientActor *target = nullptr;
	auto targetComponent = m_pAINpcPlayer->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target || target->isDead() || target->needClear())
	{
		return false;
	}

	if (m_trace)
	{
		WCoord targetpos = target->getLocoMotion()->getPosition();
		return m_pAINpcPlayer->isInHomeDist(targetpos.x, targetpos.y, targetpos.z);
	}
	else
	{
		if (atkDist(target))
		{
			return true;
		}
		
		return (!m_pAINpcPlayer->getNavigator()->noPath());
	}
}

void AIPlayerAtk::start()
{
	m_pAINpcPlayer->getNavigator()->setPath(m_PathEntity, m_Speed);
	m_PathEntity = NULL;
	m_TraceTimer = 0;
}

void AIPlayerAtk::reset()
{
	m_pAINpcPlayer->getNavigator()->clearPathEntity();
}

bool AIPlayerAtk::atkDist(ClientActor *pActor)
{
	double dist = m_AttackDistance;
	
	//CollideAABB box;
	//pActor->getCollideBox(box);
 
	dist = dist*dist;
	WCoord targetpos = pActor->getLocoMotion()->getPosition();
	if(m_pAINpcPlayer->getSquareDistToPos(targetpos.x, targetpos.y, targetpos.z) <= dist)
	{
		return true;
	}
	else
	{
		return false;
	}
}

void AIPlayerAtk::update()
{
	ClientActor *target = nullptr;
	auto targetComponent = m_pAINpcPlayer->getToAttackTargetComponent();
	if (targetComponent)
	{
		target = targetComponent->getTarget();
	}
	if (NULL == target)  return;

	m_pAINpcPlayer->setLookAt(target, 30.0, 30.0);

	ActorVision *vision = m_pAINpcPlayer->getVision();

	bool isAtkDist = atkDist(target);

	if ((m_trace || m_pAINpcPlayer->getVision()->canSeeInAICache(target)) && !isAtkDist && (--m_TraceTimer <= 0))
	{
		if(m_pAINpcPlayer->getAttrib()->getMoveSpeed()*m_Speed != 0)
		{
			m_TraceTimer = 20 + GenRandomInt(0, 6); 			
			m_pAINpcPlayer->getNavigator()->tryMoveTo(target, m_Speed);
			m_pAINpcPlayer->playAnim(SEQ_WALK);
		}
	}
 
	if (--m_AttackTick <= 0)
	{
		if (isAtkDist && m_pAINpcPlayer->getVision()->canSee(target))
		{ 
			m_AttackTick = 30;

			//if(m_pAINpcPlayer->getDef()->AttackType == ATTACK_PUNCH) //ATTACK_PUNCH = 0近程  ATTACK_RANGE = 1远程
			{
				m_pAINpcPlayer->faceWorldPos(target->getLocoMotion()->getPosition(), 180.0f, 180.0f);
				m_pAINpcPlayer->attackActor(target, SEQ_ATTACK);
			}
		}
	}
}